'use client'

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Briefcase, Building, Clock, Eye, MapPin, MessageCircle, Search, Star } from 'lucide-react'
import Link from 'next/link'
import { useSearchParams } from 'next/navigation'
import { useEffect, useState } from 'react'

// 模拟搜索结果数据
interface SearchResult {
  id: string
  type: 'company' | 'job' | 'interview' | 'post'
  title: string
  description: string
  tags: string[]
  author?: {
    name: string
    avatar?: string
  }
  location?: string
  salary?: string
  rating?: number
  viewCount?: number
  commentCount?: number
  createdAt: string
  url: string
}

const mockSearchResults: SearchResult[] = [
  {
    id: '1',
    type: 'company',
    title: '腾讯科技',
    description: '中国领先的互联网增值服务提供商，业务涵盖社交、游戏、金融科技等多个领域。',
    tags: ['互联网', '大厂', '社交', '游戏'],
    location: '深圳',
    rating: 4.5,
    viewCount: 12800,
    createdAt: '2024-01-15',
    url: '/companies/1'
  },
  {
    id: '2',
    type: 'job',
    title: '前端开发工程师',
    description: '负责公司核心产品的前端开发工作，使用React、TypeScript等技术栈。',
    tags: ['前端', 'React', 'TypeScript'],
    location: '北京',
    salary: '20-35K',
    viewCount: 856,
    createdAt: '2024-01-10',
    url: '/jobs/2'
  },
  {
    id: '3',
    type: 'interview',
    title: '阿里巴巴 Java 开发岗面试经验',
    description: '分享阿里巴巴 Java 开发岗的面试流程和问题，包括技术面和 HR 面的详细内容。',
    tags: ['阿里巴巴', 'Java', '技术面'],
    author: {
      name: '匿名用户',
      avatar: ''
    },
    viewCount: 1245,
    commentCount: 23,
    createdAt: '2024-01-08',
    url: '/companies/interviews/3'
  },
  {
    id: '4',
    type: 'post',
    title: '2024年互联网大厂薪资水平分析',
    description: '详细分析2024年各大互联网公司的薪资水平，包括基本工资、奖金、股票等。',
    tags: ['薪资', '互联网', '大厂', '分析'],
    author: {
      name: '职场观察员',
      avatar: ''
    },
    viewCount: 2156,
    commentCount: 67,
    createdAt: '2024-01-05',
    url: '/forum/4'
  },
  {
    id: '5',
    type: 'company',
    title: '字节跳动',
    description: '全球化的科技公司，旗下产品包括抖音、今日头条、TikTok等，专注于内容创作和分发。',
    tags: ['互联网', '短视频', '算法', 'AI'],
    location: '北京',
    rating: 4.3,
    viewCount: 9876,
    createdAt: '2024-01-12',
    url: '/companies/5'
  },
  {
    id: '6',
    type: 'interview',
    title: '美团后端开发面试记录',
    description: '记录美团后端开发岗位的完整面试流程，包含算法题、系统设计和项目经验讨论。',
    tags: ['美团', '后端', '算法', '系统设计'],
    author: {
      name: '求职者小李',
      avatar: ''
    },
    viewCount: 834,
    commentCount: 15,
    createdAt: '2024-01-03',
    url: '/companies/interviews/6'
  }
]

export default function SearchPage() {
  const searchParams = useSearchParams()
  const query = searchParams?.get('q') || ''
  
  const [searchQuery, setSearchQuery] = useState(query)
  const [activeTab, setActiveTab] = useState('all')
  const [sortBy, setSortBy] = useState('relevance')
  const [results, setResults] = useState<SearchResult[]>(mockSearchResults)
  
  useEffect(() => {
    // 模拟搜索逻辑
    if (searchQuery) {
      const filtered = mockSearchResults.filter(result =>
        result.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        result.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        result.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      )
      setResults(filtered)
    } else {
      setResults(mockSearchResults)
    }
  }, [searchQuery])

  const handleSearch = () => {
    // 实际项目中这里会调用搜索 API
    console.log('搜索:', searchQuery)
  }

  const filteredResults = activeTab === 'all' 
    ? results 
    : results.filter(result => result.type === activeTab)

  const sortedResults = [...filteredResults].sort((a, b) => {
    switch (sortBy) {
      case 'time':
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      case 'views':
        return (b.viewCount || 0) - (a.viewCount || 0)
      case 'rating':
        return (b.rating || 0) - (a.rating || 0)
      default:
        return 0
    }
  })

  const getResultIcon = (type: string) => {
    switch (type) {
      case 'company':
        return <Building className="h-4 w-4" />
      case 'job':
        return <Briefcase className="h-4 w-4" />
      case 'interview':
        return <MessageCircle className="h-4 w-4" />
      case 'post':
        return <MessageCircle className="h-4 w-4" />
      default:
        return <Search className="h-4 w-4" />
    }
  }

  const getResultTypeText = (type: string) => {
    switch (type) {
      case 'company':
        return '企业'
      case 'job':
        return '职位'
      case 'interview':
        return '面经'
      case 'post':
        return '帖子'
      default:
        return '其他'
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 搜索栏 */}
      <div className="mb-8">
        <div className="flex gap-4 max-w-2xl">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索企业、职位、面经、帖子..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              className="pl-9"
            />
          </div>
          <Button onClick={handleSearch}>
            搜索
          </Button>
        </div>
      </div>

      {/* 搜索结果统计 */}
      <div className="mb-6">
        <p className="text-muted-foreground">
          {searchQuery ? `搜索 "${searchQuery}" 找到 ${sortedResults.length} 个结果` : `共 ${sortedResults.length} 个结果`}
        </p>
      </div>

      {/* 筛选和排序 */}
      <div className="mb-6">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <div className="flex justify-between items-center">
            <TabsList>
              <TabsTrigger value="all">全部 ({results.length})</TabsTrigger>
              <TabsTrigger value="company">
                企业 ({results.filter(r => r.type === 'company').length})
              </TabsTrigger>
              <TabsTrigger value="job">
                职位 ({results.filter(r => r.type === 'job').length})
              </TabsTrigger>
              <TabsTrigger value="interview">
                面经 ({results.filter(r => r.type === 'interview').length})
              </TabsTrigger>
              <TabsTrigger value="post">
                帖子 ({results.filter(r => r.type === 'post').length})
              </TabsTrigger>
            </TabsList>
            
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="relevance">相关度</SelectItem>
                <SelectItem value="time">最新发布</SelectItem>
                <SelectItem value="views">浏览量</SelectItem>
                <SelectItem value="rating">评分</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          {/* 搜索结果列表 */}
          <TabsContent value={activeTab} className="mt-6">
            <div className="space-y-4">
              {sortedResults.map((result) => (
                <Card key={result.id} className="hover:shadow-lg transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <div className="flex-shrink-0">
                        <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center text-primary">
                          {getResultIcon(result.type)}
                        </div>
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-2">
                          <Badge variant="secondary" className="text-xs">
                            {getResultTypeText(result.type)}
                          </Badge>
                          {result.rating && (
                            <div className="flex items-center gap-1">
                              <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                              <span className="text-sm text-muted-foreground">{result.rating}</span>
                            </div>
                          )}
                        </div>
                        
                        <Link href={result.url} className="group">
                          <h3 className="text-lg font-semibold group-hover:text-primary transition-colors mb-2">
                            {result.title}
                          </h3>
                        </Link>
                        
                        <p className="text-muted-foreground mb-3 line-clamp-2">
                          {result.description}
                        </p>
                        
                        <div className="flex flex-wrap gap-1 mb-3">
                          {result.tags.map((tag) => (
                            <Badge key={tag} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                        
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          {result.author && (
                            <div className="flex items-center gap-1">
                              <Avatar className="h-4 w-4">
                                <AvatarImage src={result.author.avatar} />
                                <AvatarFallback className="text-xs">
                                  {result.author.name.slice(0, 1)}
                                </AvatarFallback>
                              </Avatar>
                              <span>{result.author.name}</span>
                            </div>
                          )}
                          
                          {result.location && (
                            <div className="flex items-center gap-1">
                              <MapPin className="h-3 w-3" />
                              <span>{result.location}</span>
                            </div>
                          )}
                          
                          {result.salary && (
                            <div className="font-medium text-primary">
                              {result.salary}
                            </div>
                          )}
                          
                          {result.viewCount && (
                            <div className="flex items-center gap-1">
                              <Eye className="h-3 w-3" />
                              <span>{result.viewCount.toLocaleString()}</span>
                            </div>
                          )}
                          
                          {result.commentCount && (
                            <div className="flex items-center gap-1">
                              <MessageCircle className="h-3 w-3" />
                              <span>{result.commentCount}</span>
                            </div>
                          )}
                          
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            <span>{result.createdAt}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
              
              {sortedResults.length === 0 && (
                <div className="text-center py-12">
                  <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">未找到相关结果</h3>
                  <p className="text-muted-foreground">
                    尝试使用不同的关键词或者调整筛选条件
                  </p>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
} 