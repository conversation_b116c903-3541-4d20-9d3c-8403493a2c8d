-- WorkMates 数据库视图和函数脚本
-- 创建常用的视图和存储函数

-- ================================
-- 数据库视图
-- ================================

-- 企业综合信息视图
CREATE OR REPLACE VIEW company_summary AS
SELECT
    c.id,
    c.name,
    c.name_en,
    c.industry,
    c.size,
    c.headquarters,
    c.is_verified,
    c.total_ratings,
    c.average_rating,
    c.total_salaries,
    c.total_reviews,
    c.created_at,
    -- 薪资统计
    COALESCE(s.avg_salary, 0) as avg_total_salary,
    COALESCE(s.min_salary, 0) as min_total_salary,
    COALESCE(s.max_salary, 0) as max_total_salary,
    -- 面经统计
    COALESCE(i.total_interviews, 0) as total_interviews,
    COALESCE(i.success_rate, 0) as interview_success_rate
FROM companies c
    LEFT JOIN (
        SELECT
            company_id, AVG(total_salary) as avg_salary, MIN(total_salary) as min_salary, MAX(total_salary) as max_salary
        FROM salaries
        WHERE
            is_verified = true
        GROUP BY
            company_id
    ) s ON c.id = s.company_id
    LEFT JOIN (
        SELECT
            company_id, COUNT(*) as total_interviews, ROUND(
                COUNT(
                    CASE
                        WHEN result = 'PASSED' THEN 1
                    END
                ) * 100.0 / COUNT(*), 2
            ) as success_rate
        FROM interviews
        WHERE
            result IS NOT NULL
        GROUP BY
            company_id
    ) i ON c.id = i.company_id
WHERE
    c.is_active = true;

-- 用户活跃度视图
CREATE OR REPLACE VIEW user_activity_summary AS
SELECT
    u.id,
    u.username,
    u.name,
    u.level,
    u.points,
    u.reputation,
    u.created_at,
    u.last_login,
    -- 发布统计
    COALESCE(p.post_count, 0) as total_posts,
    COALESCE(c.comment_count, 0) as total_comments,
    COALESCE(l.like_count, 0) as total_likes_given,
    COALESCE(r.received_likes, 0) as total_likes_received,
    -- 贡献统计
    COALESCE(sal.salary_count, 0) as total_salaries_shared,
    COALESCE(inter.interview_count, 0) as total_interviews_shared,
    COALESCE(rat.rating_count, 0) as total_ratings_given
FROM
    users u
    LEFT JOIN (
        SELECT author_id, COUNT(*) as post_count
        FROM posts
        WHERE
            is_published = true
        GROUP BY
            author_id
    ) p ON u.id = p.author_id
    LEFT JOIN (
        SELECT author_id, COUNT(*) as comment_count
        FROM comments
        WHERE
            is_deleted = false
        GROUP BY
            author_id
    ) c ON u.id = c.author_id
    LEFT JOIN (
        SELECT user_id, COUNT(*) as like_count
        FROM likes
        GROUP BY
            user_id
    ) l ON u.id = l.user_id
    LEFT JOIN (
        SELECT p.author_id, COUNT(lk.id) as received_likes
        FROM posts p
            JOIN likes lk ON p.id = lk.post_id
        GROUP BY
            p.author_id
    ) r ON u.id = r.author_id
    LEFT JOIN (
        SELECT author_id, COUNT(*) as salary_count
        FROM salaries
        GROUP BY
            author_id
    ) sal ON u.id = sal.author_id
    LEFT JOIN (
        SELECT author_id, COUNT(*) as interview_count
        FROM interviews
        GROUP BY
            author_id
    ) inter ON u.id = inter.author_id
    LEFT JOIN (
        SELECT author_id, COUNT(*) as rating_count
        FROM ratings
        GROUP BY
            author_id
    ) rat ON u.id = rat.author_id
WHERE
    u.is_active = true;

-- 热门帖子视图
CREATE OR REPLACE VIEW trending_posts AS
SELECT
    p.id,
    p.title,
    p.excerpt,
    p.type,
    p.category,
    p.tags,
    p.view_count,
    p.like_count,
    p.comment_count,
    p.created_at,
    p.updated_at,
    u.username as author_username,
    u.name as author_name,
    u.level as author_level,
    c.name as company_name,
    -- 热度分数计算 (可以调整权重)
    (
        p.like_count * 3 + p.comment_count * 2 + p.view_count * 0.1
    ) as popularity_score,
    -- 时间衰减的热度分数
    (
        (
            p.like_count * 3 + p.comment_count * 2 + p.view_count * 0.1
        ) * EXP(
            - EXTRACT(
                EPOCH
                FROM (NOW() - p.created_at)
            ) / 86400.0 / 7.0
        )
    ) as trending_score
FROM
    posts p
    JOIN users u ON p.author_id = u.id
    LEFT JOIN companies c ON p.company_id = c.id
WHERE
    p.is_published = true
    AND p.is_locked = false
ORDER BY trending_score DESC;

-- 薪资统计视图
CREATE OR REPLACE VIEW salary_statistics AS
SELECT
    c.name as company_name,
    s.position,
    s.work_location,
    COUNT(*) as sample_count,
    AVG(s.total_salary) as avg_total_salary,
    PERCENTILE_CONT (0.5) WITHIN GROUP (
        ORDER BY s.total_salary
    ) as median_total_salary,
    MIN(s.total_salary) as min_total_salary,
    MAX(s.total_salary) as max_total_salary,
    AVG(s.base_salary) as avg_base_salary,
    AVG(s.bonus) as avg_bonus,
    AVG(s.experience) as avg_experience,
    COUNT(
        CASE
            WHEN s.is_verified THEN 1
        END
    ) as verified_count
FROM salaries s
    JOIN companies c ON s.company_id = c.id
GROUP BY
    c.name,
    s.position,
    s.work_location
HAVING
    COUNT(*) >= 3 -- 至少有3个样本才显示
ORDER BY c.name, s.position;

-- ================================
-- 存储函数
-- ================================

-- 计算用户声誉分数的函数
CREATE OR REPLACE FUNCTION calculate_user_reputation(user_id UUID)
RETURNS FLOAT AS $$
DECLARE
    reputation_score FLOAT := 0;
    post_score FLOAT := 0;
    comment_score FLOAT := 0;
    contribution_score FLOAT := 0;
BEGIN
    -- 帖子贡献分数 (点赞数 * 2 + 评论数 * 1)
    SELECT COALESCE(SUM(like_count * 2 + comment_count * 1), 0)
    INTO post_score
    FROM posts 
    WHERE author_id = user_id AND is_published = true;
    
    -- 评论贡献分数 (评论被点赞数)
    SELECT COALESCE(SUM(like_count), 0)
    INTO comment_score
    FROM comments 
    WHERE author_id = user_id AND is_deleted = false;
    
    -- 数据贡献分数 (薪资数据 * 5 + 面经 * 3 + 评分 * 2)
    SELECT 
        COALESCE(
            (SELECT COUNT(*) FROM salaries WHERE author_id = user_id) * 5 +
            (SELECT COUNT(*) FROM interviews WHERE author_id = user_id) * 3 +
            (SELECT COUNT(*) FROM ratings WHERE author_id = user_id) * 2,
            0
        )
    INTO contribution_score;
    
    reputation_score := post_score + comment_score + contribution_score;
    
    RETURN GREATEST(reputation_score, 0);
END;
$$ LANGUAGE plpgsql;

-- 更新企业平均评分的函数
CREATE OR REPLACE FUNCTION update_company_rating(company_id UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE companies 
    SET 
        total_ratings = (
            SELECT COUNT(*) 
            FROM ratings 
            WHERE ratings.company_id = companies.id
        ),
        average_rating = (
            SELECT COALESCE(AVG(overall), 0) 
            FROM ratings 
            WHERE ratings.company_id = companies.id
        )
    WHERE id = company_id;
END;
$$ LANGUAGE plpgsql;

-- 获取企业薪资分位数的函数
CREATE OR REPLACE FUNCTION get_company_salary_percentiles(
    company_id UUID,
    position_filter TEXT DEFAULT NULL
)
RETURNS TABLE(
    percentile_25 NUMERIC,
    percentile_50 NUMERIC,
    percentile_75 NUMERIC,
    percentile_90 NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        PERCENTILE_CONT(0.25) WITHIN GROUP (ORDER BY s.total_salary) as percentile_25,
        PERCENTILE_CONT(0.50) WITHIN GROUP (ORDER BY s.total_salary) as percentile_50,
        PERCENTILE_CONT(0.75) WITHIN GROUP (ORDER BY s.total_salary) as percentile_75,
        PERCENTILE_CONT(0.90) WITHIN GROUP (ORDER BY s.total_salary) as percentile_90
    FROM salaries s
    WHERE s.company_id = get_company_salary_percentiles.company_id
      AND s.is_verified = true
      AND (position_filter IS NULL OR s.position = position_filter);
END;
$$ LANGUAGE plpgsql;

-- 搜索帖子的函数 (全文搜索)
CREATE OR REPLACE FUNCTION search_posts(
    search_query TEXT,
    post_type PostType DEFAULT NULL,
    company_filter UUID DEFAULT NULL,
    limit_count INTEGER DEFAULT 20,
    offset_count INTEGER DEFAULT 0
)
RETURNS TABLE(
    id UUID,
    title TEXT,
    excerpt TEXT,
    type PostType,
    author_name TEXT,
    company_name TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    relevance_score REAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.title,
        p.excerpt,
        p.type,
        u.name as author_name,
        c.name as company_name,
        p.created_at,
        ts_rank(
            to_tsvector('simple', p.title || ' ' || p.content),
            plainto_tsquery('simple', search_query)
        ) as relevance_score
    FROM posts p
    JOIN users u ON p.author_id = u.id
    LEFT JOIN companies c ON p.company_id = c.id
    WHERE p.is_published = true
      AND to_tsvector('simple', p.title || ' ' || p.content) @@ plainto_tsquery('simple', search_query)
      AND (post_type IS NULL OR p.type = post_type)
      AND (company_filter IS NULL OR p.company_id = company_filter)
    ORDER BY relevance_score DESC, p.created_at DESC
    LIMIT limit_count OFFSET offset_count;
END;
$$ LANGUAGE plpgsql;

-- ================================
-- 触发器函数
-- ================================

-- 自动更新帖子统计的触发器函数
CREATE OR REPLACE FUNCTION update_post_stats()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        IF TG_TABLE_NAME = 'comments' THEN
            UPDATE posts 
            SET comment_count = comment_count + 1 
            WHERE id = NEW.post_id;
        ELSIF TG_TABLE_NAME = 'likes' AND NEW.post_id IS NOT NULL THEN
            UPDATE posts 
            SET like_count = like_count + 1 
            WHERE id = NEW.post_id;
        END IF;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        IF TG_TABLE_NAME = 'comments' THEN
            UPDATE posts 
            SET comment_count = comment_count - 1 
            WHERE id = OLD.post_id;
        ELSIF TG_TABLE_NAME = 'likes' AND OLD.post_id IS NOT NULL THEN
            UPDATE posts 
            SET like_count = like_count - 1 
            WHERE id = OLD.post_id;
        END IF;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 自动更新用户声誉的触发器函数
CREATE OR REPLACE FUNCTION update_user_reputation_trigger()
RETURNS TRIGGER AS $$
DECLARE
    target_user_id UUID;
BEGIN
    -- 确定需要更新声誉的用户ID
    IF TG_TABLE_NAME = 'posts' THEN
        target_user_id := COALESCE(NEW.author_id, OLD.author_id);
    ELSIF TG_TABLE_NAME = 'comments' THEN
        target_user_id := COALESCE(NEW.author_id, OLD.author_id);
    ELSIF TG_TABLE_NAME = 'salaries' THEN
        target_user_id := COALESCE(NEW.author_id, OLD.author_id);
    ELSIF TG_TABLE_NAME = 'interviews' THEN
        target_user_id := COALESCE(NEW.author_id, OLD.author_id);
    ELSIF TG_TABLE_NAME = 'ratings' THEN
        target_user_id := COALESCE(NEW.author_id, OLD.author_id);
    END IF;
    
    -- 更新用户声誉
    IF target_user_id IS NOT NULL THEN
        UPDATE users 
        SET reputation = calculate_user_reputation(target_user_id)
        WHERE id = target_user_id;
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- ================================
-- 创建触发器
-- ================================

-- 帖子统计更新触发器
DROP TRIGGER IF EXISTS trigger_update_post_stats_comments ON comments;

CREATE TRIGGER trigger_update_post_stats_comments
    AFTER INSERT OR DELETE ON comments
    FOR EACH ROW EXECUTE FUNCTION update_post_stats();

DROP TRIGGER IF EXISTS trigger_update_post_stats_likes ON likes;

CREATE TRIGGER trigger_update_post_stats_likes
    AFTER INSERT OR DELETE ON likes
    FOR EACH ROW EXECUTE FUNCTION update_post_stats();

-- 用户声誉更新触发器
DROP TRIGGER IF EXISTS trigger_update_user_reputation_posts ON posts;

CREATE TRIGGER trigger_update_user_reputation_posts
    AFTER INSERT OR UPDATE OR DELETE ON posts
    FOR EACH ROW EXECUTE FUNCTION update_user_reputation_trigger();

DROP TRIGGER IF EXISTS trigger_update_user_reputation_salaries ON salaries;

CREATE TRIGGER trigger_update_user_reputation_salaries
    AFTER INSERT OR DELETE ON salaries
    FOR EACH ROW EXECUTE FUNCTION update_user_reputation_trigger();

-- ================================
-- 创建定期维护任务函数
-- ================================

-- 清理过期会话的函数
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM sessions 
    WHERE expires < NOW();
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 更新所有用户声誉的函数
CREATE OR REPLACE FUNCTION refresh_all_user_reputations()
RETURNS INTEGER AS $$
DECLARE
    updated_count INTEGER := 0;
    user_record RECORD;
BEGIN
    FOR user_record IN SELECT id FROM users WHERE is_active = true LOOP
        UPDATE users 
        SET reputation = calculate_user_reputation(user_record.id)
        WHERE id = user_record.id;
        
        updated_count := updated_count + 1;
    END LOOP;
    
    RETURN updated_count;
END;
$$ LANGUAGE plpgsql;

-- ================================
-- 工作经历管理系统视图和函数
-- ================================

-- 用户工作经历汇总视图
CREATE OR REPLACE VIEW user_work_experience_summary AS
SELECT
    u.id as user_id,
    u.name as user_name,
    u.email,
    COUNT(we.id) as total_experiences,
    COUNT(
        CASE
            WHEN we.verification_status = 'APPROVED' THEN 1
        END
    ) as verified_experiences,
    COUNT(
        CASE
            WHEN we.is_current = true THEN 1
        END
    ) as current_positions,
    AVG(we.verification_score) as avg_verification_score,
    STRING_AGG (
        DISTINCT we.company_name,
        ', '
        ORDER BY we.company_name
    ) as companies_worked,
    STRING_AGG (
        DISTINCT we.position,
        ', '
        ORDER BY we.position
    ) as positions_held,
    MAX(we.updated_at) as last_experience_update
FROM users u
    LEFT JOIN work_experiences we ON u.id = we.user_id
GROUP BY
    u.id,
    u.name,
    u.email;

-- 审核工作流视图

CREATE OR REPLACE VIEW pending_verifications AS
SELECT 
    'WORK_EXPERIENCE' as item_type,
    we.id as item_id,
    we.company_name as title,
    we.position as subtitle,
    u.name as submitter_name,
    u.email as submitter_email,
    we.verification_status as status,
    we.created_at as submitted_at,
    DATE_PART('day', NOW() - we.created_at) as days_pending
FROM work_experiences we
JOIN users u ON we.user_id = u.id
WHERE we.verification_status = 'PENDING'

UNION ALL

SELECT 
    'EXPERIENCE_FILE' as item_type,
    ef.id as item_id,
    ef.title as title,
    ef.file_category::text as subtitle,
    u.name as submitter_name,
    u.email as submitter_email,
    ef.verification_status::text as status,
    ef.uploaded_at as submitted_at,
    DATE_PART('day', NOW() - ef.uploaded_at) as days_pending
FROM experience_files ef
JOIN work_experiences we ON ef.work_experience_id = we.id
JOIN users u ON we.user_id = u.id
WHERE ef.verification_status = 'PENDING'

ORDER BY submitted_at ASC;

-- 用户信誉详情视图
CREATE OR REPLACE VIEW user_credibility_details AS
SELECT
    u.id as user_id,
    u.name as user_name,
    u.email,
    u.level as user_level,
    uc.overall_score,
    uc.work_experience_score,
    uc.salary_contribution_score,
    uc.interview_contribution_score,
    uc.verified_experiences_count,
    uc.verified_files_count,
    uc.verified_salaries_count,
    uc.verified_interviews_count,
    uc.total_contributions_count,
    uc.helpful_contributions_count,
    uc.flagged_contributions_count,
    uc.last_calculated_at,
    CASE
        WHEN uc.overall_score >= 0.9 THEN 'EXCELLENT'
        WHEN uc.overall_score >= 0.8 THEN 'GOOD'
        WHEN uc.overall_score >= 0.6 THEN 'FAIR'
        WHEN uc.overall_score >= 0.4 THEN 'POOR'
        ELSE 'VERY_POOR'
    END as credibility_rating
FROM users u
    LEFT JOIN user_credibility uc ON u.id = uc.user_id;

-- 审核统计视图
CREATE OR REPLACE VIEW verification_statistics AS
SELECT
    target_type,
    decision,
    COUNT(*) as total_records,
    AVG(confidence_level) as avg_confidence,
    COUNT(DISTINCT reviewer_id) as unique_reviewers,
    MIN(created_at) as earliest_review,
    MAX(created_at) as latest_review
FROM verification_records
GROUP BY
    target_type,
    decision
ORDER BY target_type, decision;

-- ================================
-- 工作经历管理函数
-- ================================

-- 计算用户信誉分数的函数
CREATE OR REPLACE FUNCTION calculate_user_credibility_score(p_user_id UUID)
RETURNS VOID AS $$
DECLARE
    v_work_exp_score DECIMAL(3,2) := 0.0;
    v_salary_score DECIMAL(3,2) := 0.0;
    v_interview_score DECIMAL(3,2) := 0.0;
    v_overall_score DECIMAL(3,2) := 0.0;
    v_verified_exp_count INTEGER := 0;
    v_verified_file_count INTEGER := 0;
    v_verified_salary_count INTEGER := 0;
    v_verified_interview_count INTEGER := 0;
    v_total_contributions INTEGER := 0;
    v_helpful_contributions INTEGER := 0;
    v_flagged_contributions INTEGER := 0;
BEGIN
    -- 计算工作经历可信度分数
    SELECT 
        COALESCE(AVG(verification_score), 0.0),
        COUNT(CASE WHEN verification_status = 'APPROVED' THEN 1 END)
    INTO v_work_exp_score, v_verified_exp_count
    FROM work_experiences 
    WHERE user_id = p_user_id;
    
    -- 计算文件验证数量
    SELECT COUNT(*)
    INTO v_verified_file_count
    FROM experience_files ef
    JOIN work_experiences we ON ef.work_experience_id = we.id
    WHERE we.user_id = p_user_id AND ef.verification_status = 'APPROVED';
    
    -- 计算薪资贡献可信度分数
    SELECT 
        CASE WHEN COUNT(*) > 0 THEN 
            (COUNT(CASE WHEN is_verified = true THEN 1 END)::DECIMAL / COUNT(*)) * 1.0
        ELSE 0.0 END,
        COUNT(CASE WHEN is_verified = true THEN 1 END)
    INTO v_salary_score, v_verified_salary_count
    FROM salaries 
    WHERE author_id = p_user_id;
    
    -- 计算面试贡献可信度分数
    SELECT 
        CASE WHEN COUNT(*) > 0 THEN 0.8 ELSE 0.0 END,
        COUNT(*)
    INTO v_interview_score, v_verified_interview_count
    FROM interviews 
    WHERE author_id = p_user_id;
    
    -- 计算贡献统计
    SELECT 
        COUNT(*),
        COUNT(CASE WHEN r.status != 'REJECTED' OR r.status IS NULL THEN 1 END),
        COUNT(CASE WHEN r.status = 'REJECTED' THEN 1 END)
    INTO v_total_contributions, v_helpful_contributions, v_flagged_contributions
    FROM (
        SELECT p.id FROM posts p WHERE p.author_id = p_user_id
        UNION ALL
        SELECT s.id FROM salaries s WHERE s.author_id = p_user_id
        UNION ALL
        SELECT i.id FROM interviews i WHERE i.author_id = p_user_id
        UNION ALL
        SELECT we.id FROM work_experiences we WHERE we.user_id = p_user_id
    ) contributions
    LEFT JOIN reports r ON r.post_id::text = contributions.id::text;
    
    -- 计算总体信誉分数 (加权平均)
    v_overall_score := (
        v_work_exp_score * 0.4 +
        v_salary_score * 0.3 +
        v_interview_score * 0.2 +
        LEAST(v_total_contributions / 10.0, 1.0) * 0.1
    );
    
    -- 更新或插入用户信誉记录
    INSERT INTO user_credibility (
        user_id, overall_score, work_experience_score, salary_contribution_score,
        interview_contribution_score, verified_experiences_count, verified_files_count,
        verified_salaries_count, verified_interviews_count, total_contributions_count,
        helpful_contributions_count, flagged_contributions_count, last_calculated_at
    ) VALUES (
        p_user_id, v_overall_score, v_work_exp_score, v_salary_score,
        v_interview_score, v_verified_exp_count, v_verified_file_count,
        v_verified_salary_count, v_verified_interview_count, v_total_contributions,
        v_helpful_contributions, v_flagged_contributions, NOW()
    )
    ON CONFLICT (user_id) DO UPDATE SET
        overall_score = EXCLUDED.overall_score,
        work_experience_score = EXCLUDED.work_experience_score,
        salary_contribution_score = EXCLUDED.salary_contribution_score,
        interview_contribution_score = EXCLUDED.interview_contribution_score,
        verified_experiences_count = EXCLUDED.verified_experiences_count,
        verified_files_count = EXCLUDED.verified_files_count,
        verified_salaries_count = EXCLUDED.verified_salaries_count,
        verified_interviews_count = EXCLUDED.verified_interviews_count,
        total_contributions_count = EXCLUDED.total_contributions_count,
        helpful_contributions_count = EXCLUDED.helpful_contributions_count,
        flagged_contributions_count = EXCLUDED.flagged_contributions_count,
        last_calculated_at = EXCLUDED.last_calculated_at,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- 工作经历验证触发器函数
CREATE OR REPLACE FUNCTION trigger_update_user_credibility()
RETURNS TRIGGER AS $$
DECLARE
    target_user_id UUID;
BEGIN
    -- 确定需要更新信誉的用户ID
    IF TG_TABLE_NAME = 'work_experiences' THEN
        target_user_id := COALESCE(NEW.user_id, OLD.user_id);
    ELSIF TG_TABLE_NAME = 'experience_files' THEN
        SELECT we.user_id INTO target_user_id
        FROM work_experiences we
        WHERE we.id = COALESCE(NEW.work_experience_id, OLD.work_experience_id);
    ELSIF TG_TABLE_NAME = 'verification_records' THEN
        -- 根据审核记录更新相关用户的信誉
        IF COALESCE(NEW.target_type, OLD.target_type) = 'WORK_EXPERIENCE' THEN
            SELECT we.user_id INTO target_user_id
            FROM work_experiences we
            WHERE we.id = COALESCE(NEW.target_id, OLD.target_id);
        ELSIF COALESCE(NEW.target_type, OLD.target_type) = 'EXPERIENCE_FILE' THEN
            SELECT we.user_id INTO target_user_id
            FROM experience_files ef
            JOIN work_experiences we ON ef.work_experience_id = we.id
            WHERE ef.id = COALESCE(NEW.target_id, OLD.target_id);
        END IF;
    END IF;
    
    -- 更新用户信誉分数
    IF target_user_id IS NOT NULL THEN
        PERFORM calculate_user_credibility_score(target_user_id);
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- 获取用户工作经历统计的函数
CREATE OR REPLACE FUNCTION get_user_experience_stats(p_user_id UUID)
RETURNS TABLE(
    total_experiences INTEGER,
    verified_experiences INTEGER,
    pending_experiences INTEGER,
    total_years_experience INTEGER,
    companies_count INTEGER,
    current_company TEXT,
    avg_verification_score DECIMAL(3,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*)::INTEGER as total_experiences,
        COUNT(CASE WHEN we.verification_status = 'APPROVED' THEN 1 END)::INTEGER as verified_experiences,
        COUNT(CASE WHEN we.verification_status = 'PENDING' THEN 1 END)::INTEGER as pending_experiences,
        COALESCE(SUM(
            CASE 
                WHEN we.end_date IS NOT NULL THEN 
                    EXTRACT(YEAR FROM we.end_date - we.start_date)
                ELSE 
                    EXTRACT(YEAR FROM NOW() - we.start_date)
            END
        ), 0)::INTEGER as total_years_experience,
        COUNT(DISTINCT we.company_name)::INTEGER as companies_count,
        (SELECT we2.company_name FROM work_experiences we2 
         WHERE we2.user_id = p_user_id AND we2.is_current = true 
         LIMIT 1) as current_company,
        COALESCE(AVG(we.verification_score), 0.0)::DECIMAL(3,2) as avg_verification_score
    FROM work_experiences we
    WHERE we.user_id = p_user_id;
END;
$$ LANGUAGE plpgsql;

-- ================================
-- 创建工作经历相关触发器
-- ================================

-- 工作经历变更时更新用户信誉
DROP TRIGGER IF EXISTS trigger_work_experiences_credibility_update ON work_experiences;

CREATE TRIGGER trigger_work_experiences_credibility_update
    AFTER INSERT OR UPDATE OR DELETE ON work_experiences
    FOR EACH ROW EXECUTE FUNCTION trigger_update_user_credibility();

-- 文件验证变更时更新用户信誉
DROP TRIGGER IF EXISTS trigger_experience_files_credibility_update ON experience_files;

CREATE TRIGGER trigger_experience_files_credibility_update
    AFTER INSERT OR UPDATE OR DELETE ON experience_files
    FOR EACH ROW EXECUTE FUNCTION trigger_update_user_credibility();

-- 审核记录变更时更新用户信誉
DROP TRIGGER IF EXISTS trigger_verification_records_credibility_update ON verification_records;

CREATE TRIGGER trigger_verification_records_credibility_update
    AFTER INSERT OR UPDATE OR DELETE ON verification_records
    FOR EACH ROW EXECUTE FUNCTION trigger_update_user_credibility();

-- ================================
-- 使用示例
-- ================================

-- 创建视图和函数后的使用示例：

-- 1. 查看企业综合信息
-- SELECT * FROM company_summary WHERE average_rating > 4.0;

-- 2. 查看用户活跃度
-- SELECT * FROM user_activity_summary ORDER BY total_posts DESC LIMIT 10;

-- 3. 查看热门帖子
-- SELECT * FROM trending_posts LIMIT 20;

-- 4. 搜索帖子
-- SELECT * FROM search_posts('前端面试经验', 'SHARING'::PostType);

-- 5. 获取企业薪资分位数
-- SELECT * FROM get_company_salary_percentiles('660e8400-e29b-41d4-a716-446655440001');

-- 6. 手动更新用户声誉
-- SELECT calculate_user_reputation('550e8400-e29b-41d4-a716-446655440002');

-- 7. 查看用户工作经历汇总
-- SELECT * FROM user_work_experience_summary WHERE verified_experiences > 0;

-- 8. 查看待审核项目
-- SELECT * FROM pending_verifications ORDER BY days_pending DESC;

-- 9. 查看用户信誉详情
-- SELECT * FROM user_credibility_details WHERE credibility_rating = 'EXCELLENT';

-- 10. 更新用户信誉分数
-- SELECT calculate_user_credibility_score('550e8400-e29b-41d4-a716-446655440002');

-- 11. 获取用户工作经历统计
-- SELECT * FROM get_user_experience_stats('550e8400-e29b-41d4-a716-446655440002');

COMMIT;