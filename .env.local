# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://zfctpeukxaxftfsmpqgp.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpmY3RwZXVreGF4ZnRmc21wcWdwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3NjUyMTYsImV4cCI6MjA2NzM0MTIxNn0.CuPOCJ-RkxXNQb3A7yeTuS0WLtXQ6tF7gkUUEkO3Dx0


# Connect to Supabase via connection pooling
DATABASE_URL="postgresql://postgres.zfctpeukxaxftfsmpqgp:<EMAIL>:6543/postgres?pgbouncer=true"
# Direct connection to the database. Used for migrations
DIRECT_URL="postgresql://postgres.zfctpeukxaxftfsmpqgp:<EMAIL>:5432/postgres"


# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=workmates-dev-secret-key-2024

# Google OAuth Configuration
GOOGLE_CLIENT_ID=232576410919-16b9kb88t7vba23flua5luhib18kof0l.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
