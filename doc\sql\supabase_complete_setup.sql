-- ========================================
-- WorkMates 完整数据库初始化脚本
-- 适用于 Supabase 平台
-- 执行顺序：在 Supabase Dashboard > SQL Editor 中依次执行
-- ========================================

-- ================================
-- 第一部分：枚举类型定义
-- ================================

-- 用户等级枚举
CREATE TYPE "UserLevel" AS ENUM (
  'NEWBIE',     -- 新手
  'ACTIVE',     -- 活跃用户
  'SENIOR',     -- 资深用户
  'EXPERT',     -- 专家
  'MODERATOR',  -- 版主
  'ADMIN'       -- 管理员
);

-- 企业规模枚举
CREATE TYPE "CompanySize" AS ENUM (
  'STARTUP',    -- 创业公司 (1-50人)
  'SMALL',      -- 小型公司 (51-200人)
  'MEDIUM',     -- 中型公司 (201-1000人)
  'LARGE',      -- 大型公司 (1001-5000人)
  'ENTERPRISE'  -- 超大型企业 (5000+人)
);

-- 帖子类型枚举
CREATE TYPE "PostType" AS ENUM (
  'DISCUSSION', -- 讨论
  'QUESTION',   -- 问题
  'SHARING',    -- 分享
  'NEWS',       -- 新闻
  'REVIEW',     -- 评价
  'JOB'         -- 招聘
);

-- 面试难度枚举
CREATE TYPE "InterviewDifficulty" AS ENUM (
  'EASY',
  'MEDIUM',
  'HARD',
  'VERY_HARD'
);

-- 面试结果枚举
CREATE TYPE "InterviewResult" AS ENUM (
  'PASSED',     -- 通过
  'FAILED',     -- 未通过
  'PENDING',    -- 等待结果
  'CANCELLED'   -- 取消
);

-- 举报原因枚举
CREATE TYPE "ReportReason" AS ENUM (
  'SPAM',         -- 垃圾信息
  'INAPPROPRIATE', -- 不当内容
  'FAKE_INFO',    -- 虚假信息
  'HARASSMENT',   -- 骚扰
  'COPYRIGHT',    -- 版权侵犯
  'OTHER'         -- 其他
);

-- 举报状态枚举
CREATE TYPE "ReportStatus" AS ENUM (
  'PENDING',    -- 待处理
  'REVIEWING',  -- 审核中
  'RESOLVED',   -- 已解决
  'REJECTED'    -- 已拒绝
);

-- 雇佣类型枚举
CREATE TYPE "EmploymentType" AS ENUM (
  'FULL_TIME',  -- 全职
  'PART_TIME',  -- 兼职
  'CONTRACT',   -- 合同工
  'INTERNSHIP', -- 实习
  'FREELANCE'   -- 自由职业
);

-- 审核状态枚举
CREATE TYPE "VerificationStatus" AS ENUM (
  'PENDING',    -- 待审核
  'APPROVED',   -- 已通过
  'REJECTED',   -- 已拒绝
  'REVOKED'     -- 已撤销
);

-- 文件类型枚举
CREATE TYPE "FileCategory" AS ENUM (
  'CONTRACT',   -- 合同
  'CERTIFICATE',-- 证书
  'PHOTO',      -- 照片
  'DOCUMENT',   -- 文档
  'OTHER'       -- 其他
);

-- 审核目标类型枚举
CREATE TYPE "TargetType" AS ENUM (
  'WORK_EXPERIENCE', -- 工作经历
  'EXPERIENCE_FILE', -- 经历文件
  'SALARY',         -- 薪资信息
  'INTERVIEW'       -- 面试经历
);

-- 审核决定枚举
CREATE TYPE "ReviewDecision" AS ENUM (
  'APPROVED',        -- 通过
  'REJECTED',        -- 拒绝
  'REVOKED',         -- 撤销
  'PENDING_MORE_INFO' -- 需要更多信息
);

-- ================================
-- 第二部分：自定义函数
-- ================================

-- 更新时间戳函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- UUID生成函数 (确保兼容性)
CREATE OR REPLACE FUNCTION generate_uuid() RETURNS UUID AS $$
BEGIN
  RETURN gen_random_uuid();
END;
$$ LANGUAGE plpgsql;

-- ================================
-- 第三部分：数据表创建
-- ================================

-- 用户表
CREATE TABLE IF NOT EXISTS "users" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "email" VARCHAR(255) UNIQUE NOT NULL,
    "username" VARCHAR(100) UNIQUE,
    "phone" VARCHAR(20) UNIQUE,
    "password" VARCHAR(255),
    "name" VARCHAR(100),
    "avatar" VARCHAR(500),
    "bio" TEXT,
    -- 职业信息
    "position" VARCHAR(100),
    "company" VARCHAR(200),
    "experience" INTEGER,
    "industry" VARCHAR(100),
    "education" VARCHAR(200),
    "skills" TEXT[] DEFAULT '{}',
    -- 用户等级和积分
    "level" "UserLevel" DEFAULT 'NEWBIE',
    "points" INTEGER DEFAULT 0,
    "reputation" DECIMAL(10, 2) DEFAULT 0.0,
    -- 隐私设置
    "isAnonymous" BOOLEAN DEFAULT false,
    "isEmailPublic" BOOLEAN DEFAULT false,
    "isPhonePublic" BOOLEAN DEFAULT false,
    -- 账户状态
    "isVerified" BOOLEAN DEFAULT false,
    "isActive" BOOLEAN DEFAULT true,
    "isBanned" BOOLEAN DEFAULT false,
    -- 时间戳
    "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "lastLogin" TIMESTAMP WITH TIME ZONE,
    -- 约束
    CONSTRAINT check_reputation CHECK ("reputation" >= 0),
    CONSTRAINT check_points CHECK ("points" >= 0),
    CONSTRAINT check_experience CHECK ("experience" >= 0)
);

-- NextAuth.js 账户表
CREATE TABLE IF NOT EXISTS "accounts" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid (),
    "userId" UUID NOT NULL,
    "type" VARCHAR(50) NOT NULL,
    "provider" VARCHAR(50) NOT NULL,
    "providerAccountId" VARCHAR(100) NOT NULL,
    "refresh_token" TEXT,
    "access_token" TEXT,
    "expires_at" INTEGER,
    "token_type" VARCHAR(50),
    "scope" VARCHAR(200),
    "id_token" TEXT,
    "session_state" VARCHAR(200),
    CONSTRAINT fk_accounts_user FOREIGN KEY ("userId") REFERENCES "users" ("id") ON DELETE CASCADE,
    CONSTRAINT unique_provider_account UNIQUE (
        "provider",
        "providerAccountId"
    )
);

-- NextAuth.js 会话表
CREATE TABLE IF NOT EXISTS "sessions" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid (),
    "sessionToken" VARCHAR(255) UNIQUE NOT NULL,
    "userId" UUID NOT NULL,
    "expires" TIMESTAMP
    WITH
        TIME ZONE NOT NULL,
        CONSTRAINT fk_sessions_user FOREIGN KEY ("userId") REFERENCES "users" ("id") ON DELETE CASCADE
);

-- NextAuth.js 验证令牌表
CREATE TABLE IF NOT EXISTS "verification_tokens" (
    "identifier" VARCHAR(255) NOT NULL,
    "token" VARCHAR(255) UNIQUE NOT NULL,
    "expires" TIMESTAMP
    WITH
        TIME ZONE NOT NULL,
        CONSTRAINT unique_identifier_token UNIQUE ("identifier", "token")
);

-- 企业表
CREATE TABLE IF NOT EXISTS "companies" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid (),
    "name" VARCHAR(200) UNIQUE NOT NULL,
    "nameEn" VARCHAR(200) UNIQUE,
    "logo" VARCHAR(500),
    "description" TEXT,
    "website" VARCHAR(500),
    -- 基本信息
    "industry" VARCHAR(100),
    "size" "CompanySize",
    "foundedYear" INTEGER,
    "headquarters" VARCHAR(100),
    -- 联系信息
    "address" TEXT,
    "phone" VARCHAR(50),
    "email" VARCHAR(255),
    -- 企业状态
    "isVerified" BOOLEAN DEFAULT false,
    "isActive" BOOLEAN DEFAULT true,
    -- 统计信息
    "totalRatings" INTEGER DEFAULT 0,
    "averageRating" DECIMAL(3, 2) DEFAULT 0.0,
    "totalSalaries" INTEGER DEFAULT 0,
    "totalReviews" INTEGER DEFAULT 0,
    -- 时间戳
    "createdAt" TIMESTAMP
    WITH
        TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP
    WITH
        TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        -- 约束
        CONSTRAINT check_founded_year CHECK (
            "foundedYear" >= 1800
            AND "foundedYear" <= EXTRACT(
                YEAR
                FROM CURRENT_DATE
            )
        ),
        CONSTRAINT check_average_rating CHECK (
            "averageRating" >= 0
            AND "averageRating" <= 5
        ),
        CONSTRAINT check_total_ratings CHECK ("totalRatings" >= 0),
        CONSTRAINT check_total_salaries CHECK ("totalSalaries" >= 0),
        CONSTRAINT check_total_reviews CHECK ("totalReviews" >= 0)
);

-- 帖子表
CREATE TABLE IF NOT EXISTS "posts" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "title" VARCHAR(500) NOT NULL,
    "content" TEXT NOT NULL,
    "excerpt" TEXT,
    -- 帖子类型
    "type" "PostType" DEFAULT 'DISCUSSION',
    "category" VARCHAR(100),
    "tags" TEXT[] DEFAULT '{}',
    -- 关联信息
    "companyId" UUID,
    "authorId" UUID NOT NULL,
    "isAnonymous" BOOLEAN DEFAULT false,
    -- 状态
    "isPublished" BOOLEAN DEFAULT false,
    "isPinned" BOOLEAN DEFAULT false,
    "isLocked" BOOLEAN DEFAULT false,
    -- 统计
    "viewCount" INTEGER DEFAULT 0,
    "likeCount" INTEGER DEFAULT 0,
    "commentCount" INTEGER DEFAULT 0,
    -- 时间戳
    "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "publishedAt" TIMESTAMP WITH TIME ZONE,
    -- 外键约束
    CONSTRAINT fk_posts_company FOREIGN KEY ("companyId") REFERENCES "companies" ("id") ON DELETE SET NULL,
    CONSTRAINT fk_posts_author FOREIGN KEY ("authorId") REFERENCES "users" ("id") ON DELETE CASCADE,
    -- 检查约束
    CONSTRAINT check_view_count CHECK ("viewCount" >= 0),
    CONSTRAINT check_like_count CHECK ("likeCount" >= 0),
    CONSTRAINT check_comment_count CHECK ("commentCount" >= 0)
);

-- 评论表
CREATE TABLE IF NOT EXISTS "comments" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid (),
    "content" TEXT NOT NULL,
    -- 关联信息
    "postId" UUID NOT NULL,
    "authorId" UUID NOT NULL,
    -- 回复功能
    "parentId" UUID,
    -- 状态
    "isAnonymous" BOOLEAN DEFAULT false,
    "isDeleted" BOOLEAN DEFAULT false,
    -- 统计
    "likeCount" INTEGER DEFAULT 0,
    -- 时间戳
    "createdAt" TIMESTAMP
    WITH
        TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP
    WITH
        TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        -- 外键约束
        CONSTRAINT fk_comments_post FOREIGN KEY ("postId") REFERENCES "posts" ("id") ON DELETE CASCADE,
        CONSTRAINT fk_comments_author FOREIGN KEY ("authorId") REFERENCES "users" ("id") ON DELETE CASCADE,
        CONSTRAINT fk_comments_parent FOREIGN KEY ("parentId") REFERENCES "comments" ("id") ON DELETE CASCADE,
        -- 检查约束
        CONSTRAINT check_comment_like_count CHECK ("likeCount" >= 0)
);

-- 点赞表
CREATE TABLE IF NOT EXISTS "likes" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid (),
    "userId" UUID NOT NULL,
    -- 被点赞的内容 (互斥)
    "postId" UUID,
    "commentId" UUID,
    "createdAt" TIMESTAMP
    WITH
        TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        -- 外键约束
        CONSTRAINT fk_likes_user FOREIGN KEY ("userId") REFERENCES "users" ("id") ON DELETE CASCADE,
        CONSTRAINT fk_likes_post FOREIGN KEY ("postId") REFERENCES "posts" ("id") ON DELETE CASCADE,
        CONSTRAINT fk_likes_comment FOREIGN KEY ("commentId") REFERENCES "comments" ("id") ON DELETE CASCADE,
        -- 唯一约束 (用户对同一内容只能点赞一次)
        CONSTRAINT unique_user_post_like UNIQUE ("userId", "postId"),
        CONSTRAINT unique_user_comment_like UNIQUE ("userId", "commentId"),
        -- 检查约束 (确保只点赞一种内容类型)
        CONSTRAINT check_like_target CHECK (
            (
                "postId" IS NOT NULL
                AND "commentId" IS NULL
            )
            OR (
                "postId" IS NULL
                AND "commentId" IS NOT NULL
            )
        )
);

-- 收藏表
CREATE TABLE IF NOT EXISTS "bookmarks" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid (),
    "userId" UUID NOT NULL,
    "postId" UUID NOT NULL,
    "createdAt" TIMESTAMP
    WITH
        TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        -- 外键约束
        CONSTRAINT fk_bookmarks_user FOREIGN KEY ("userId") REFERENCES "users" ("id") ON DELETE CASCADE,
        CONSTRAINT fk_bookmarks_post FOREIGN KEY ("postId") REFERENCES "posts" ("id") ON DELETE CASCADE,
        -- 唯一约束
        CONSTRAINT unique_user_post_bookmark UNIQUE ("userId", "postId")
);

-- 薪资表
CREATE TABLE IF NOT EXISTS "salaries" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "authorId" UUID NOT NULL,
    "companyId" UUID NOT NULL,
    -- 职位信息
    "position" VARCHAR(200) NOT NULL,
    "level" VARCHAR(100),
    "workLocation" VARCHAR(100),
    "workType" "EmploymentType" DEFAULT 'FULL_TIME',
    -- 经验信息
    "experience" INTEGER NOT NULL,
    "education" VARCHAR(200),
    "skills" TEXT[] DEFAULT '{}',
    -- 薪资信息
    "baseSalary" DECIMAL(12, 2) NOT NULL,
    "bonus" DECIMAL(12, 2) DEFAULT 0,
    "stockOptions" DECIMAL(12, 2) DEFAULT 0,
    "otherBenefits" DECIMAL(12, 2) DEFAULT 0,
    "totalSalary" DECIMAL(12, 2) NOT NULL,
    -- 额外信息
    "workHours" VARCHAR(100),
    "benefits" TEXT,
    "notes" TEXT,
    -- 状态
    "isAnonymous" BOOLEAN DEFAULT false,
    "isVerified" BOOLEAN DEFAULT false,
    "isActive" BOOLEAN DEFAULT true,
    -- 时间戳
    "salaryYear" INTEGER NOT NULL,
    "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    -- 外键约束
    CONSTRAINT fk_salaries_author FOREIGN KEY ("authorId") REFERENCES "users" ("id") ON DELETE CASCADE,
    CONSTRAINT fk_salaries_company FOREIGN KEY ("companyId") REFERENCES "companies" ("id") ON DELETE CASCADE,
    -- 检查约束
    CONSTRAINT check_salary_year CHECK ("salaryYear" >= 2000 AND "salaryYear" <= EXTRACT(YEAR FROM CURRENT_DATE) + 1),
    CONSTRAINT check_experience_positive CHECK ("experience" >= 0),
    CONSTRAINT check_base_salary_positive CHECK ("baseSalary" > 0),
    CONSTRAINT check_total_salary_calculation CHECK ("totalSalary" = "baseSalary" + "bonus" + "stockOptions" + "otherBenefits")
);

-- 面试表
CREATE TABLE IF NOT EXISTS "interviews" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "authorId" UUID NOT NULL,
    "companyId" UUID NOT NULL,
    -- 职位信息
    "position" VARCHAR(200) NOT NULL,
    "department" VARCHAR(100),
    "interviewDate" DATE NOT NULL,
    -- 面试过程
    "rounds" INTEGER DEFAULT 1,
    "duration" INTEGER, -- 分钟
    "difficulty" "InterviewDifficulty" DEFAULT 'MEDIUM',
    "result" "InterviewResult" DEFAULT 'PENDING',
    -- 面试内容
    "questions" TEXT,
    "experience" TEXT,
    "feedback" TEXT,
    "tips" TEXT,
    -- 技能要求
    "skills" TEXT[] DEFAULT '{}',
    "requiredExperience" INTEGER,
    -- 状态
    "isAnonymous" BOOLEAN DEFAULT false,
    "isHelpful" BOOLEAN DEFAULT true,
    "isActive" BOOLEAN DEFAULT true,
    -- 时间戳
    "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    -- 外键约束
    CONSTRAINT fk_interviews_author FOREIGN KEY ("authorId") REFERENCES "users" ("id") ON DELETE CASCADE,
    CONSTRAINT fk_interviews_company FOREIGN KEY ("companyId") REFERENCES "companies" ("id") ON DELETE CASCADE,
    -- 检查约束
    CONSTRAINT check_rounds_positive CHECK ("rounds" > 0),
    CONSTRAINT check_duration_reasonable CHECK ("duration" IS NULL OR ("duration" > 0 AND "duration" <= 720)),
    CONSTRAINT check_required_experience CHECK ("requiredExperience" IS NULL OR "requiredExperience" >= 0),
    CONSTRAINT check_interview_date CHECK ("interviewDate" <= CURRENT_DATE)
);

-- 评分表
CREATE TABLE IF NOT EXISTS "ratings" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid (),
    "authorId" UUID NOT NULL,
    "companyId" UUID NOT NULL,
    -- 评分项
    "overallRating" DECIMAL(2, 1) NOT NULL,
    "workLifeBalance" DECIMAL(2, 1),
    "compensation" DECIMAL(2, 1),
    "culture" DECIMAL(2, 1),
    "careerDevelopment" DECIMAL(2, 1),
    "management" DECIMAL(2, 1),
    -- 评价内容
    "title" VARCHAR(200),
    "content" TEXT,
    "pros" TEXT,
    "cons" TEXT,
    "advice" TEXT,
    -- 背景信息
    "position" VARCHAR(200),
    "department" VARCHAR(100),
    "employmentStatus" "EmploymentType",
    "workDuration" INTEGER, -- 工作月数
    -- 状态
    "isAnonymous" BOOLEAN DEFAULT false,
    "isRecommended" BOOLEAN DEFAULT true,
    "isActive" BOOLEAN DEFAULT true,
    -- 时间戳
    "createdAt" TIMESTAMP
    WITH
        TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP
    WITH
        TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        -- 外键约束
        CONSTRAINT fk_ratings_author FOREIGN KEY ("authorId") REFERENCES "users" ("id") ON DELETE CASCADE,
        CONSTRAINT fk_ratings_company FOREIGN KEY ("companyId") REFERENCES "companies" ("id") ON DELETE CASCADE,
        -- 检查约束
        CONSTRAINT check_overall_rating CHECK (
            "overallRating" >= 1.0
            AND "overallRating" <= 5.0
        ),
        CONSTRAINT check_work_life_balance CHECK (
            "workLifeBalance" IS NULL
            OR (
                "workLifeBalance" >= 1.0
                AND "workLifeBalance" <= 5.0
            )
        ),
        CONSTRAINT check_compensation_rating CHECK (
            "compensation" IS NULL
            OR (
                "compensation" >= 1.0
                AND "compensation" <= 5.0
            )
        ),
        CONSTRAINT check_culture_rating CHECK (
            "culture" IS NULL
            OR (
                "culture" >= 1.0
                AND "culture" <= 5.0
            )
        ),
        CONSTRAINT check_career_development CHECK (
            "careerDevelopment" IS NULL
            OR (
                "careerDevelopment" >= 1.0
                AND "careerDevelopment" <= 5.0
            )
        ),
        CONSTRAINT check_management_rating CHECK (
            "management" IS NULL
            OR (
                "management" >= 1.0
                AND "management" <= 5.0
            )
        ),
        CONSTRAINT check_work_duration CHECK (
            "workDuration" IS NULL
            OR "workDuration" > 0
        ),
        -- 唯一约束 (每个用户对每个公司只能评价一次)
        CONSTRAINT unique_user_company_rating UNIQUE ("authorId", "companyId")
);

-- 举报表
CREATE TABLE IF NOT EXISTS "reports" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "reporterId" UUID NOT NULL,
    "reason" "ReportReason" NOT NULL,
    "description" TEXT,
    "status" "ReportStatus" DEFAULT 'PENDING',
    -- 被举报的内容 (互斥)
    "postId" UUID,
    "commentId" UUID,
    "userId" UUID,
    "salaryId" UUID,
    "interviewId" UUID,
    "ratingId" UUID,
    -- 处理信息
    "handlerId" UUID,
    "handlerNotes" TEXT,
    "handledAt" TIMESTAMP WITH TIME ZONE,
    -- 时间戳
    "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    -- 外键约束
    CONSTRAINT fk_reports_reporter FOREIGN KEY ("reporterId") REFERENCES "users" ("id") ON DELETE CASCADE,
    CONSTRAINT fk_reports_handler FOREIGN KEY ("handlerId") REFERENCES "users" ("id") ON DELETE SET NULL,
    CONSTRAINT fk_reports_post FOREIGN KEY ("postId") REFERENCES "posts" ("id") ON DELETE CASCADE,
    CONSTRAINT fk_reports_comment FOREIGN KEY ("commentId") REFERENCES "comments" ("id") ON DELETE CASCADE,
    CONSTRAINT fk_reports_user FOREIGN KEY ("userId") REFERENCES "users" ("id") ON DELETE CASCADE,
    CONSTRAINT fk_reports_salary FOREIGN KEY ("salaryId") REFERENCES "salaries" ("id") ON DELETE CASCADE,
    CONSTRAINT fk_reports_interview FOREIGN KEY ("interviewId") REFERENCES "interviews" ("id") ON DELETE CASCADE,
    CONSTRAINT fk_reports_rating FOREIGN KEY ("ratingId") REFERENCES "ratings" ("id") ON DELETE CASCADE,
    -- 检查约束 (确保只举报一种内容类型)
    CONSTRAINT check_report_target CHECK (
        (("postId" IS NOT NULL)::int + ("commentId" IS NOT NULL)::int + ("userId" IS NOT NULL)::int + 
         ("salaryId" IS NOT NULL)::int + ("interviewId" IS NOT NULL)::int + ("ratingId" IS NOT NULL)::int) = 1
    )
);

-- 工作经历表
CREATE TABLE IF NOT EXISTS "work_experiences" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "userId" UUID NOT NULL,
    "companyName" VARCHAR(200) NOT NULL,
    "position" VARCHAR(200) NOT NULL,
    "department" VARCHAR(100),
    "employmentType" "EmploymentType" DEFAULT 'FULL_TIME',
    "startDate" DATE NOT NULL,
    "endDate" DATE,
    "isCurrent" BOOLEAN DEFAULT false,
    "location" VARCHAR(100),
    "description" TEXT,
    "responsibilities" TEXT,
    "achievements" TEXT,
    "skills" TEXT[] DEFAULT '{}',
    "salary" DECIMAL(12, 2),
    "verificationStatus" "VerificationStatus" DEFAULT 'PENDING',
    "verifiedById" UUID,
    "verificationNotes" TEXT,
    "isPublic" BOOLEAN DEFAULT false,
    "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    -- 外键约束
    CONSTRAINT fk_work_experiences_user FOREIGN KEY ("userId") REFERENCES "users" ("id") ON DELETE CASCADE,
    CONSTRAINT fk_work_experiences_verifier FOREIGN KEY ("verifiedById") REFERENCES "users" ("id") ON DELETE SET NULL,
    -- 检查约束
    CONSTRAINT check_work_dates CHECK ("startDate" <= COALESCE("endDate", CURRENT_DATE)),
    CONSTRAINT check_current_work CHECK (
        ("isCurrent" = true AND "endDate" IS NULL) OR 
        ("isCurrent" = false)
    ),
    CONSTRAINT check_salary_positive CHECK ("salary" IS NULL OR "salary" > 0)
);

-- 经历文件表
CREATE TABLE IF NOT EXISTS "experience_files" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid (),
    "workExperienceId" UUID NOT NULL,
    "fileName" VARCHAR(255) NOT NULL,
    "fileUrl" VARCHAR(500) NOT NULL,
    "fileSize" INTEGER,
    "mimeType" VARCHAR(100),
    "category" "FileCategory" DEFAULT 'DOCUMENT',
    "description" TEXT,
    "verificationStatus" "VerificationStatus" DEFAULT 'PENDING',
    "verifiedById" UUID,
    "verificationNotes" TEXT,
    "uploadedAt" TIMESTAMP
    WITH
        TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        "createdAt" TIMESTAMP
    WITH
        TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP
    WITH
        TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        -- 外键约束
        CONSTRAINT fk_experience_files_work_experience FOREIGN KEY ("workExperienceId") REFERENCES "work_experiences" ("id") ON DELETE CASCADE,
        CONSTRAINT fk_experience_files_verifier FOREIGN KEY ("verifiedById") REFERENCES "users" ("id") ON DELETE SET NULL,
        -- 检查约束
        CONSTRAINT check_file_size CHECK (
            "fileSize" IS NULL
            OR "fileSize" > 0
        )
);

-- 审核记录表
CREATE TABLE IF NOT EXISTS "verification_records" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid (),
    "reviewerId" UUID NOT NULL,
    "targetType" "TargetType" NOT NULL,
    "targetId" UUID NOT NULL,
    "decision" "ReviewDecision" NOT NULL,
    "notes" TEXT,
    "evidence" TEXT,
    "createdAt" TIMESTAMP
    WITH
        TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        -- 外键约束
        CONSTRAINT fk_verification_records_reviewer FOREIGN KEY ("reviewerId") REFERENCES "users" ("id") ON DELETE CASCADE
);

-- 用户可信度表
CREATE TABLE IF NOT EXISTS "user_credibility" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid (),
    "userId" UUID UNIQUE NOT NULL,
    "totalVerifications" INTEGER DEFAULT 0,
    "successfulVerifications" INTEGER DEFAULT 0,
    "credibilityScore" DECIMAL(5, 2) DEFAULT 0.0,
    "lastCalculatedAt" TIMESTAMP
    WITH
        TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        "createdAt" TIMESTAMP
    WITH
        TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" TIMESTAMP
    WITH
        TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        -- 外键约束
        CONSTRAINT fk_user_credibility_user FOREIGN KEY ("userId") REFERENCES "users" ("id") ON DELETE CASCADE,
        -- 检查约束
        CONSTRAINT check_total_verifications CHECK ("totalVerifications" >= 0),
        CONSTRAINT check_successful_verifications CHECK (
            "successfulVerifications" >= 0
            AND "successfulVerifications" <= "totalVerifications"
        ),
        CONSTRAINT check_credibility_score CHECK (
            "credibilityScore" >= 0
            AND "credibilityScore" <= 100
        )
);

-- ================================
-- 第四部分：触发器创建
-- ================================

-- 用户表更新时间触发器
CREATE TRIGGER trigger_users_updated_at
    BEFORE UPDATE ON "users"
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 企业表更新时间触发器
CREATE TRIGGER trigger_companies_updated_at
    BEFORE UPDATE ON "companies"
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 帖子表更新时间触发器
CREATE TRIGGER trigger_posts_updated_at
    BEFORE UPDATE ON "posts"
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 评论表更新时间触发器
CREATE TRIGGER trigger_comments_updated_at
    BEFORE UPDATE ON "comments"
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 薪资表更新时间触发器
CREATE TRIGGER trigger_salaries_updated_at
    BEFORE UPDATE ON "salaries"
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 面试表更新时间触发器
CREATE TRIGGER trigger_interviews_updated_at
    BEFORE UPDATE ON "interviews"
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 评分表更新时间触发器
CREATE TRIGGER trigger_ratings_updated_at
    BEFORE UPDATE ON "ratings"
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 举报表更新时间触发器
CREATE TRIGGER trigger_reports_updated_at
    BEFORE UPDATE ON "reports"
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 工作经历表更新时间触发器
CREATE TRIGGER trigger_work_experiences_updated_at
    BEFORE UPDATE ON "work_experiences"
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 用户可信度表更新时间触发器
CREATE TRIGGER trigger_user_credibility_updated_at
    BEFORE UPDATE ON "user_credibility"
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- ================================
-- 第五部分：性能优化索引
-- ================================

-- 用户表索引
CREATE INDEX IF NOT EXISTS idx_users_email ON "users" ("email");

CREATE INDEX IF NOT EXISTS idx_users_username ON "users" ("username");

CREATE INDEX IF NOT EXISTS idx_users_level ON "users" ("level");

CREATE INDEX IF NOT EXISTS idx_users_points ON "users" ("points");

CREATE INDEX IF NOT EXISTS idx_users_reputation ON "users" ("reputation");

CREATE INDEX IF NOT EXISTS idx_users_is_active ON "users" ("isActive");

CREATE INDEX IF NOT EXISTS idx_users_created_at ON "users" ("createdAt");

CREATE INDEX IF NOT EXISTS idx_users_last_login ON "users" ("lastLogin");

-- 企业表索引
CREATE INDEX IF NOT EXISTS idx_companies_name ON "companies" ("name");

CREATE INDEX IF NOT EXISTS idx_companies_industry ON "companies" ("industry");

CREATE INDEX IF NOT EXISTS idx_companies_size ON "companies" ("size");

CREATE INDEX IF NOT EXISTS idx_companies_is_verified ON "companies" ("isVerified");

CREATE INDEX IF NOT EXISTS idx_companies_is_active ON "companies" ("isActive");

CREATE INDEX IF NOT EXISTS idx_companies_average_rating ON "companies" ("averageRating");

CREATE INDEX IF NOT EXISTS idx_companies_total_ratings ON "companies" ("totalRatings");

CREATE INDEX IF NOT EXISTS idx_companies_created_at ON "companies" ("createdAt");

-- 帖子表索引
CREATE INDEX IF NOT EXISTS idx_posts_author_id ON "posts" ("authorId");

CREATE INDEX IF NOT EXISTS idx_posts_company_id ON "posts" ("companyId");

CREATE INDEX IF NOT EXISTS idx_posts_type ON "posts" ("type");

CREATE INDEX IF NOT EXISTS idx_posts_category ON "posts" ("category");

CREATE INDEX IF NOT EXISTS idx_posts_is_published ON "posts" ("isPublished");

CREATE INDEX IF NOT EXISTS idx_posts_is_pinned ON "posts" ("isPinned");

CREATE INDEX IF NOT EXISTS idx_posts_view_count ON "posts" ("viewCount");

CREATE INDEX IF NOT EXISTS idx_posts_like_count ON "posts" ("likeCount");

CREATE INDEX IF NOT EXISTS idx_posts_comment_count ON "posts" ("commentCount");

CREATE INDEX IF NOT EXISTS idx_posts_created_at ON "posts" ("createdAt");

CREATE INDEX IF NOT EXISTS idx_posts_updated_at ON "posts" ("updatedAt");

CREATE INDEX IF NOT EXISTS idx_posts_published_at ON "posts" ("publishedAt");

-- 复合索引 - 热门帖子查询优化
CREATE INDEX IF NOT EXISTS idx_posts_published_trending ON "posts" (
    "isPublished",
    "like_count",
    "comment_count",
    "created_at"
)
WHERE
    "isPublished" = true;

-- 全文搜索索引
CREATE INDEX IF NOT EXISTS idx_posts_title_search ON "posts" USING gin (
    to_tsvector ('chinese', "title")
);

CREATE INDEX IF NOT EXISTS idx_posts_content_search ON "posts" USING gin (
    to_tsvector ('chinese', "content")
);

-- 评论表索引
CREATE INDEX IF NOT EXISTS idx_comments_post_id ON "comments" ("postId");

CREATE INDEX IF NOT EXISTS idx_comments_author_id ON "comments" ("authorId");

CREATE INDEX IF NOT EXISTS idx_comments_parent_id ON "comments" ("parentId");

CREATE INDEX IF NOT EXISTS idx_comments_is_deleted ON "comments" ("isDeleted");

CREATE INDEX IF NOT EXISTS idx_comments_like_count ON "comments" ("likeCount");

CREATE INDEX IF NOT EXISTS idx_comments_created_at ON "comments" ("createdAt");

-- 点赞表索引
CREATE INDEX IF NOT EXISTS idx_likes_user_id ON "likes" ("userId");

CREATE INDEX IF NOT EXISTS idx_likes_post_id ON "likes" ("postId");

CREATE INDEX IF NOT EXISTS idx_likes_comment_id ON "likes" ("commentId");

CREATE INDEX IF NOT EXISTS idx_likes_created_at ON "likes" ("createdAt");

-- 收藏表索引
CREATE INDEX IF NOT EXISTS idx_bookmarks_user_id ON "bookmarks" ("userId");

CREATE INDEX IF NOT EXISTS idx_bookmarks_post_id ON "bookmarks" ("postId");

CREATE INDEX IF NOT EXISTS idx_bookmarks_created_at ON "bookmarks" ("createdAt");

-- 薪资表索引
CREATE INDEX IF NOT EXISTS idx_salaries_author_id ON "salaries" ("authorId");

CREATE INDEX IF NOT EXISTS idx_salaries_company_id ON "salaries" ("companyId");

CREATE INDEX IF NOT EXISTS idx_salaries_position ON "salaries" ("position");

CREATE INDEX IF NOT EXISTS idx_salaries_work_location ON "salaries" ("workLocation");

CREATE INDEX IF NOT EXISTS idx_salaries_work_type ON "salaries" ("workType");

CREATE INDEX IF NOT EXISTS idx_salaries_experience ON "salaries" ("experience");

CREATE INDEX IF NOT EXISTS idx_salaries_total_salary ON "salaries" ("totalSalary");

CREATE INDEX IF NOT EXISTS idx_salaries_salary_year ON "salaries" ("salaryYear");

CREATE INDEX IF NOT EXISTS idx_salaries_is_verified ON "salaries" ("isVerified");

CREATE INDEX IF NOT EXISTS idx_salaries_is_active ON "salaries" ("isActive");

CREATE INDEX IF NOT EXISTS idx_salaries_created_at ON "salaries" ("createdAt");

-- 薪资表复合索引 - 统计查询优化
CREATE INDEX IF NOT EXISTS idx_salaries_company_position_year ON "salaries" (
    "companyId",
    "position",
    "salaryYear"
)
WHERE
    "isActive" = true;

CREATE INDEX IF NOT EXISTS idx_salaries_verified_stats ON "salaries" (
    "companyId",
    "position",
    "isVerified",
    "totalSalary"
)
WHERE
    "isActive" = true;

-- 面试表索引
CREATE INDEX IF NOT EXISTS idx_interviews_author_id ON "interviews" ("authorId");

CREATE INDEX IF NOT EXISTS idx_interviews_company_id ON "interviews" ("companyId");

CREATE INDEX IF NOT EXISTS idx_interviews_position ON "interviews" ("position");

CREATE INDEX IF NOT EXISTS idx_interviews_difficulty ON "interviews" ("difficulty");

CREATE INDEX IF NOT EXISTS idx_interviews_result ON "interviews" ("result");

CREATE INDEX IF NOT EXISTS idx_interviews_interview_date ON "interviews" ("interviewDate");

CREATE INDEX IF NOT EXISTS idx_interviews_is_active ON "interviews" ("isActive");

CREATE INDEX IF NOT EXISTS idx_interviews_created_at ON "interviews" ("createdAt");

-- 评分表索引
CREATE INDEX IF NOT EXISTS idx_ratings_author_id ON "ratings" ("authorId");

CREATE INDEX IF NOT EXISTS idx_ratings_company_id ON "ratings" ("companyId");

CREATE INDEX IF NOT EXISTS idx_ratings_overall_rating ON "ratings" ("overallRating");

CREATE INDEX IF NOT EXISTS idx_ratings_is_recommended ON "ratings" ("isRecommended");

CREATE INDEX IF NOT EXISTS idx_ratings_is_active ON "ratings" ("isActive");

CREATE INDEX IF NOT EXISTS idx_ratings_created_at ON "ratings" ("createdAt");

-- 举报表索引
CREATE INDEX IF NOT EXISTS idx_reports_reporter_id ON "reports" ("reporterId");

CREATE INDEX IF NOT EXISTS idx_reports_handler_id ON "reports" ("handlerId");

CREATE INDEX IF NOT EXISTS idx_reports_status ON "reports" ("status");

CREATE INDEX IF NOT EXISTS idx_reports_reason ON "reports" ("reason");

CREATE INDEX IF NOT EXISTS idx_reports_created_at ON "reports" ("createdAt");

-- 工作经历表索引
CREATE INDEX IF NOT EXISTS idx_work_experiences_user_id ON "work_experiences" ("userId");

CREATE INDEX IF NOT EXISTS idx_work_experiences_company_name ON "work_experiences" ("companyName");

CREATE INDEX IF NOT EXISTS idx_work_experiences_position ON "work_experiences" ("position");

CREATE INDEX IF NOT EXISTS idx_work_experiences_employment_type ON "work_experiences" ("employmentType");

CREATE INDEX IF NOT EXISTS idx_work_experiences_verification_status ON "work_experiences" ("verificationStatus");

CREATE INDEX IF NOT EXISTS idx_work_experiences_is_current ON "work_experiences" ("isCurrent");

CREATE INDEX IF NOT EXISTS idx_work_experiences_is_public ON "work_experiences" ("isPublic");

CREATE INDEX IF NOT EXISTS idx_work_experiences_start_date ON "work_experiences" ("startDate");

CREATE INDEX IF NOT EXISTS idx_work_experiences_end_date ON "work_experiences" ("endDate");

CREATE INDEX IF NOT EXISTS idx_work_experiences_created_at ON "work_experiences" ("createdAt");

-- 经历文件表索引
CREATE INDEX IF NOT EXISTS idx_experience_files_work_experience_id ON "experience_files" ("workExperienceId");

CREATE INDEX IF NOT EXISTS idx_experience_files_category ON "experience_files" ("category");

CREATE INDEX IF NOT EXISTS idx_experience_files_verification_status ON "experience_files" ("verificationStatus");

CREATE INDEX IF NOT EXISTS idx_experience_files_verified_by_id ON "experience_files" ("verifiedById");

CREATE INDEX IF NOT EXISTS idx_experience_files_uploaded_at ON "experience_files" ("uploadedAt");

CREATE INDEX IF NOT EXISTS idx_experience_files_created_at ON "experience_files" ("createdAt");

-- 审核记录表索引
CREATE INDEX IF NOT EXISTS idx_verification_records_reviewer_id ON "verification_records" ("reviewerId");

CREATE INDEX IF NOT EXISTS idx_verification_records_target_type ON "verification_records" ("targetType");

CREATE INDEX IF NOT EXISTS idx_verification_records_target_id ON "verification_records" ("targetId");

CREATE INDEX IF NOT EXISTS idx_verification_records_decision ON "verification_records" ("decision");

CREATE INDEX IF NOT EXISTS idx_verification_records_created_at ON "verification_records" ("createdAt");

-- 用户可信度表索引
CREATE INDEX IF NOT EXISTS idx_user_credibility_user_id ON "user_credibility" ("userId");

CREATE INDEX IF NOT EXISTS idx_user_credibility_score ON "user_credibility" ("credibilityScore");

CREATE INDEX IF NOT EXISTS idx_user_credibility_last_calculated ON "user_credibility" ("lastCalculatedAt");

-- ================================
-- 第六部分：基础种子数据 (可选)
-- ================================

-- 插入示例企业数据
INSERT INTO
    "companies" (
        "name",
        "nameEn",
        "industry",
        "size",
        "foundedYear",
        "headquarters",
        "description",
        "isVerified",
        "isActive"
    )
VALUES (
        '腾讯科技',
        'Tencent',
        '互联网科技',
        'ENTERPRISE',
        1998,
        '深圳',
        '中国领先的互联网增值服务提供商',
        true,
        true
    ),
    (
        '阿里巴巴集团',
        'Alibaba Group',
        '电子商务',
        'ENTERPRISE',
        1999,
        '杭州',
        '全球最大的在线和移动商务公司',
        true,
        true
    ),
    (
        '字节跳动',
        'ByteDance',
        '互联网科技',
        'ENTERPRISE',
        2012,
        '北京',
        '全球化的移动互联网平台',
        true,
        true
    ),
    (
        '百度公司',
        'Baidu',
        '人工智能',
        'LARGE',
        2000,
        '北京',
        '全球最大的中文搜索引擎',
        true,
        true
    ),
    (
        '美团',
        'Meituan',
        '生活服务',
        'LARGE',
        2010,
        '北京',
        '中国领先的生活服务电子商务平台',
        true,
        true
    ),
    (
        '小米科技',
        'Xiaomi',
        '智能硬件',
        'LARGE',
        2010,
        '北京',
        '以手机、智能硬件和IoT平台为核心的互联网公司',
        true,
        true
    ),
    (
        '华为技术',
        'Huawei',
        '通信设备',
        'ENTERPRISE',
        1987,
        '深圳',
        '全球领先的ICT基础设施和智能终端提供商',
        true,
        true
    ),
    (
        '网易公司',
        'NetEase',
        '互联网科技',
        'LARGE',
        1997,
        '杭州',
        '中国领先的互联网技术公司',
        true,
        true
    ),
    (
        '滴滴出行',
        'DiDi',
        '出行服务',
        'LARGE',
        2012,
        '北京',
        '全球领先的移动出行平台',
        true,
        true
    ),
    (
        '京东集团',
        'JD.com',
        '电子商务',
        'ENTERPRISE',
        1998,
        '北京',
        '中国领先的技术驱动的电商和零售基础设施服务商',
        true,
        true
    ) ON CONFLICT ("name") DO NOTHING;

-- 插入示例管理员用户
INSERT INTO
    "users" (
        "email",
        "username",
        "name",
        "level",
        "points",
        "reputation",
        "isVerified",
        "isActive"
    )
VALUES (
        '<EMAIL>',
        'admin',
        '系统管理员',
        'ADMIN',
        10000,
        100.0,
        true,
        true
    ) ON CONFLICT ("email") DO NOTHING;

-- ================================
-- 执行完成提示
-- ================================

-- 创建一个简单的查询来验证安装
DO $$ BEGIN RAISE NOTICE '===========================================';

RAISE NOTICE 'WorkMates 数据库初始化完成！';

RAISE NOTICE '===========================================';

RAISE NOTICE '已创建表数量: %',
(
    SELECT count(*)
    FROM information_schema.tables
    WHERE
        table_schema = 'public'
        AND table_type = 'BASE TABLE'
);

RAISE NOTICE '已创建枚举类型数量: %',
(
    SELECT count(*)
    FROM pg_type
    WHERE
        typname IN (
            'UserLevel',
            'CompanySize',
            'PostType',
            'InterviewDifficulty',
            'InterviewResult',
            'ReportReason',
            'ReportStatus',
            'EmploymentType',
            'VerificationStatus',
            'FileCategory',
            'TargetType',
            'ReviewDecision'
        )
);

RAISE NOTICE '已创建索引数量: %',
(
    SELECT count(*)
    FROM pg_indexes
    WHERE
        schemaname = 'public'
);

RAISE NOTICE '已创建企业数量: %',
(
    SELECT count(*)
    FROM companies
);

RAISE NOTICE '===========================================';

RAISE NOTICE '✅ 数据库已准备就绪，可以开始使用！';

RAISE NOTICE '🔗 项目URL: https://zfctpeukxaxftfsmpqgp.supabase.co';

RAISE NOTICE '📊 请在 Supabase Dashboard 中查看数据表';

RAISE NOTICE '===========================================';

END $$;