import { auth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'

// 创建公司的验证模式
const createCompanySchema = z.object({
  name: z.string().min(1, '公司名称不能为空').max(200),
  nameEn: z.string().max(200).optional(),
  logo: z.string().url().max(500).optional(),
  description: z.string().optional(),
  website: z.string().url().max(500).optional(),
  industry: z.string().max(100).optional(),
  size: z
    .enum(['STARTUP', 'SMALL', 'MEDIUM', 'LARGE', 'ENTERPRISE'])
    .optional(),
  foundedYear: z
    .number()
    .int()
    .min(1800)
    .max(new Date().getFullYear())
    .optional(),
  headquarters: z.string().max(100).optional(),
  address: z.string().optional(),
  phone: z.string().max(50).optional(),
  email: z.string().email().max(255).optional(),
})

/**
 * 获取公司列表
 * GET /api/companies
 *
 * 查询参数:
 * - page: 页码 (默认 1)
 * - limit: 每页数量 (默认 20)
 * - search: 搜索关键词
 * - industry: 行业筛选
 * - size: 公司规模筛选
 * - sort: 排序字段 (name, createdAt, averageRating)
 * - order: 排序方向 (asc, desc)
 */
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '20')
    const search = url.searchParams.get('search')
    const industry = url.searchParams.get('industry')
    const size = url.searchParams.get('size')
    const sort = url.searchParams.get('sort') || 'createdAt'
    const order = url.searchParams.get('order') || 'desc'

    // 构建查询条件
    const where: any = {
      isActive: true, // 只显示激活的公司
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { nameEn: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ]
    }

    if (industry) {
      where.industry = industry
    }

    if (size) {
      where.size = size
    }

    // 构建排序条件
    const orderBy: any = {}
    if (sort === 'name') {
      orderBy.name = order
    } else if (sort === 'averageRating') {
      orderBy.averageRating = order
    } else {
      orderBy.createdAt = order
    }

    // 执行查询
    const [companies, total] = await Promise.all([
      prisma.company.findMany({
        where,
        skip: (page - 1) * limit,
        take: limit,
        select: {
          id: true,
          name: true,
          nameEn: true,
          logo: true,
          description: true,
          industry: true,
          size: true,
          headquarters: true,
          isVerified: true,
          averageRating: true,
          totalRatings: true,
          totalSalaries: true,
          totalReviews: true,
          createdAt: true,
        },
        orderBy,
      }),
      prisma.company.count({ where }),
    ])

    return NextResponse.json({
      success: true,
      message: '获取公司列表成功',
      data: companies,
      meta: {
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
        timestamp: new Date().toISOString(),
        version: '1.0',
      },
    })
  } catch (error) {
    console.error('获取公司列表失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '获取公司列表失败',
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}

/**
 * 创建公司
 * POST /api/companies
 *
 * 需要管理员权限
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户权限
    const session = await auth()
    if (!session?.user) {
      return NextResponse.json(
        {
          success: false,
          message: '需要登录',
          error: {
            code: 'UNAUTHORIZED',
            message: '请先登录',
          },
        },
        { status: 401 }
      )
    }

    // 检查用户权限 - 只有管理员可以创建公司
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { level: true },
    })

    if (user?.level !== 'ADMIN' && user?.level !== 'MODERATOR') {
      return NextResponse.json(
        {
          success: false,
          message: '权限不足',
          error: {
            code: 'INSUFFICIENT_PERMISSION',
            message: '只有管理员或版主可以创建企业信息',
          },
        },
        { status: 403 }
      )
    }

    const body = await request.json()

    // 验证输入数据
    const validatedData = createCompanySchema.parse(body)

    // 检查公司是否已存在
    const existingCompany = await prisma.company.findFirst({
      where: {
        OR: [
          { name: validatedData.name },
          { nameEn: validatedData.nameEn || undefined },
        ].filter(Boolean),
      },
    })

    if (existingCompany) {
      return NextResponse.json(
        {
          success: false,
          message: '公司已存在',
          error: {
            code: 'DUPLICATE_COMPANY',
            message: '该公司名称已被使用',
          },
        },
        { status: 409 }
      )
    }

    // 创建公司
    const company = await prisma.company.create({
      data: {
        ...validatedData,
        isVerified: false, // 新创建的公司默认未验证
        isActive: true,
      },
    })

    return NextResponse.json(
      {
        success: true,
        message: '创建公司成功',
        data: company,
        meta: {
          timestamp: new Date().toISOString(),
          version: '1.0',
        },
      },
      { status: 201 }
    )
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: '输入数据验证失败',
          error: {
            code: 'VALIDATION_ERROR',
            message: '输入数据格式不正确',
            details: error.errors.reduce(
              (acc, err) => {
                acc[err.path.join('.')] = err.message
                return acc
              },
              {} as Record<string, string>
            ),
          },
        },
        { status: 400 }
      )
    }

    console.error('创建公司失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '创建公司失败',
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}
