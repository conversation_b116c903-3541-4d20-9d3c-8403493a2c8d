require('dotenv').config({ path: '.env.local' })
const { createClient } = require('@supabase/supabase-js')
const bcrypt = require('bcryptjs')

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
)

async function createTestUsers() {
  console.log('Creating test users...\n')

  try {
    // 创建测试用户数据
    const testUsers = [
      {
        email: '<EMAIL>',
        username: 'testuser1',
        password: await bcrypt.hash('password123', 10),
        name: '张三',
        position: '前端工程师',
        company: '腾讯',
        experience: 3,
        industry: '互联网',
        education: '本科',
        skills: ['JavaScript', 'React', 'TypeScript'],
        level: 'ACTIVE',
        points: 100,
        reputation: 4.2,
        isVerified: true,
        isActive: true,
      },
      {
        email: '<EMAIL>',
        username: 'testuser2',
        password: await bcrypt.hash('password123', 10),
        name: '李四',
        position: '后端工程师',
        company: '阿里巴巴',
        experience: 5,
        industry: '互联网',
        education: '硕士',
        skills: ['Java', 'Spring', 'MySQL'],
        level: 'SENIOR',
        points: 250,
        reputation: 4.5,
        isVerified: true,
        isActive: true,
      },
      {
        email: '<EMAIL>',
        username: 'testuser3',
        password: await bcrypt.hash('password123', 10),
        name: '王五',
        position: '产品经理',
        company: '字节跳动',
        experience: 4,
        industry: '互联网',
        education: '本科',
        skills: ['产品设计', '数据分析', '项目管理'],
        level: 'ACTIVE',
        points: 180,
        reputation: 4.0,
        isVerified: false,
        isActive: true,
      },
    ]

    console.log('1. 插入测试用户...')
    const { data: insertedUsers, error: insertError } = await supabase
      .from('users')
      .insert(testUsers)
      .select('id, email, name, position')

    if (insertError) {
      console.log('❌ 插入用户失败:', insertError.message)
      return null
    }

    console.log('✅ 成功创建用户:')
    insertedUsers.forEach((user, index) => {
      console.log(
        `   ${index + 1}. ${user.name} (${user.email}) - ${user.position}`
      )
    })

    return insertedUsers
  } catch (error) {
    console.error('❌ 创建用户失败:', error.message)
    return null
  }
}

async function createTestRatingsWithUsers() {
  console.log('\n2. 创建评价数据...')

  // 首先创建用户
  const users = await createTestUsers()
  if (!users) return

  // 获取公司数据
  const { data: companies, error: companyError } = await supabase
    .from('companies')
    .select('id, name')
    .limit(3)

  if (companyError || !companies?.length) {
    console.log('❌ 无法获取公司信息:', companyError?.message)
    return
  }

  console.log(
    '\n✅ 找到公司:',
    companies.map(c => c.name)
  )

  // 创建测试评价数据
  const testRatings = [
    {
      authorId: users[0].id,
      companyId: companies[0].id,
      overallRating: 4.5,
      workLifeBalance: 4,
      compensation: 4,
      culture: 5,
      careerGrowth: 4,
      management: 3,
      title: '优秀的技术公司',
      pros: '技术氛围浓厚，同事专业素质高，学习机会多',
      cons: '工作强度较大，加班相对较多',
      advice: '适合有技术追求的工程师发展',
      isRecommended: true,
      recommendationReason: '技术成长空间大',
      position: '前端工程师',
      department: '技术部',
      workDuration: 2,
      employmentType: 'FULL_TIME',
      isAnonymous: false,
      isActive: true,
      isVerified: false,
    },
    {
      authorId: users[1].id,
      companyId: companies[0].id,
      overallRating: 3.5,
      workLifeBalance: 2,
      compensation: 5,
      culture: 4,
      careerGrowth: 3,
      management: 3,
      title: '薪资不错但工作强度大',
      pros: '薪资水平行业领先，福利待遇好',
      cons: '996工作制，工作压力很大',
      advice: '适合能承受高强度工作的人',
      isRecommended: false,
      recommendationReason: '工作生活平衡较差',
      position: '后端工程师',
      department: '技术部',
      workDuration: 1,
      employmentType: 'FULL_TIME',
      isAnonymous: true,
      isActive: true,
      isVerified: false,
    },
    {
      authorId: users[2].id,
      companyId: companies[1].id,
      overallRating: 4.2,
      workLifeBalance: 5,
      compensation: 3,
      culture: 4,
      careerGrowth: 4,
      management: 4,
      title: '工作环境轻松，适合长期发展',
      pros: '工作氛围轻松，同事关系融洽，很少加班',
      cons: '薪资相对较低，晋升机会有限',
      advice: '适合追求工作生活平衡的人',
      isRecommended: true,
      recommendationReason: '生活质量高',
      position: '产品经理',
      department: '产品部',
      workDuration: 3,
      employmentType: 'FULL_TIME',
      isAnonymous: false,
      isActive: true,
      isVerified: false,
    },
  ]

  console.log('\n3. 插入评价数据...')
  const { data: insertedRatings, error: ratingError } = await supabase
    .from('ratings')
    .insert(testRatings)
    .select(
      'id, title, overallRating, workLifeBalance, compensation, culture, careerGrowth'
    )

  if (ratingError) {
    console.log('❌ 插入评价数据失败:', ratingError.message)
    return
  }

  console.log('✅ 成功创建评价数据:')
  insertedRatings.forEach((rating, index) => {
    console.log(
      `   ${index + 1}. ${rating.title} (评分: ${rating.overallRating})`
    )
    console.log(`      - 工作生活平衡: ${rating.workLifeBalance}`)
    console.log(`      - 薪资待遇: ${rating.compensation}`)
    console.log(`      - 企业文化: ${rating.culture}`)
    console.log(`      - 职业发展: ${rating.careerGrowth}`)
  })

  console.log('\n🎉 测试数据创建完成！所有字段名验证正确。')
}

createTestRatingsWithUsers()
