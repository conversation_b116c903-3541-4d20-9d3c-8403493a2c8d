# WorkMates 项目结构说明

## 目录结构概览

```
WorkMates/
├── doc/                              # 项目文档
│   ├── design/                       # 设计文档
│   │   ├── WorkMates项目需求文档.md   # 项目需求文档
│   │   └── 项目结构说明.md            # 本文档
│   └── sql/                          # SQL脚本文件
├── src/                              # 源代码目录
│   ├── app/                          # Next.js 15 App Router
│   │   ├── (auth)/                   # 认证相关页面组
│   │   │   ├── login/                # 登录页面
│   │   │   ├── register/             # 注册页面
│   │   │   └── layout.tsx            # 认证页面布局
│   │   ├── (main)/                   # 主要功能页面组
│   │   │   ├── companies/            # 企业信息页面
│   │   │   │   ├── [id]/             # 企业详情页
│   │   │   │   │   └── reviews/      # 企业评价相关
│   │   │   │   │       ├── page.tsx  # 企业评价列表
│   │   │   │   │       └── submit/   # 提交企业评价
│   │   │   │   ├── interviews/       # 面试经验页面
│   │   │   │   │   ├── [id]/         # 面试详情页
│   │   │   │   │   ├── page.tsx      # 面试经验列表
│   │   │   │   │   └── submit/       # 提交面试经验
│   │   │   │   ├── salaries/         # 薪资信息页面
│   │   │   │   │   ├── page.tsx      # 薪资数据展示
│   │   │   │   │   └── submit/       # 提交薪资数据
│   │   │   │   └── page.tsx          # 企业列表页
│   │   │   ├── forum/                # 论坛相关页面
│   │   │   │   ├── [id]/             # 帖子详情页
│   │   │   │   ├── create/           # 发帖页面
│   │   │   │   └── page.tsx          # 论坛首页
│   │   │   ├── profile/              # 用户中心
│   │   │   │   ├── favorites/        # 用户收藏夹
│   │   │   │   ├── messages/         # 用户消息中心
│   │   │   │   ├── settings/         # 用户设置
│   │   │   │   ├── work-experience/  # 工作经验管理
│   │   │   │   └── page.tsx          # 个人主页
│   │   │   └── layout.tsx            # 主要页面布局
│   │   ├── about/                    # 关于我们页面
│   │   ├── help/                     # 帮助中心页面
│   │   ├── privacy/                  # 隐私政策页面
│   │   ├── search/                   # 全局搜索结果页面
│   │   ├── terms/                    # 服务条款页面
│   │   ├── api/                      # API 路由
│   │   │   ├── auth/                 # 认证相关API
│   │   │   ├── companies/            # 企业相关API
│   │   │   ├── posts/                # 帖子相关API
│   │   │   ├── salaries/             # 薪资相关API
│   │   │   └── users/                # 用户相关API
│   │   ├── globals.css               # 全局样式
│   │   ├── layout.tsx                # 根布局
│   │   └── page.tsx                  # 首页
│   ├── components/                   # 可复用组件
│   │   ├── ui/                       # 基础UI组件(shadcn/ui)
│   │   │   ├── button.tsx            # 按钮组件
│   │   │   ├── card.tsx              # 卡片组件
│   │   │   ├── input.tsx             # 输入框组件
│   │   │   ├── dialog.tsx            # 对话框组件
│   │   │   └── ...                   # 其他UI组件
│   │   ├── forms/                    # 表单组件
│   │   │   ├── auth-form.tsx         # 认证表单
│   │   │   ├── company-form.tsx      # 企业信息表单
│   │   │   └── post-form.tsx         # 发帖表单
│   │   ├── layout/                   # 布局组件
│   │   │   ├── header.tsx            # 页头组件
│   │   │   ├── footer.tsx            # 页脚组件
│   │   │   ├── sidebar.tsx           # 侧边栏组件
│   │   │   └── navigation.tsx        # 导航组件
│   │   ├── features/                 # 功能特定组件
│   │   │   ├── companies/            # 企业相关组件
│   │   │   ├── forum/                # 论坛相关组件
│   │   │   ├── salaries/             # 薪资相关组件
│   │   │   └── users/                # 用户相关组件
│   │   └── providers.tsx             # Context Providers
│   ├── lib/                          # 工具库和配置
│   │   ├── auth.ts                   # NextAuth.js 配置
│   │   ├── db.ts                     # 数据库连接(Prisma)
│   │   ├── utils.ts                  # 通用工具函数
│   │   ├── validations.ts            # 数据验证(Zod)
│   │   ├── constants.ts              # 常量定义
│   │   └── hooks/                    # 自定义Hooks
│   │       ├── use-auth.ts           # 认证相关Hook
│   │       ├── use-companies.ts      # 企业数据Hook
│   │       └── use-posts.ts          # 帖子数据Hook
│   ├── types/                        # TypeScript类型定义
│   │   ├── auth.ts                   # 认证相关类型
│   │   ├── company.ts                # 企业相关类型
│   │   ├── post.ts                   # 帖子相关类型
│   │   ├── user.ts                   # 用户相关类型
│   │   └── api.ts                    # API响应类型
│   └── styles/                       # 样式文件
│       └── components.css            # 组件特定样式
├── prisma/                           # Prisma数据库配置
│   ├── schema.prisma                 # 数据库模式
│   ├── migrations/                   # 数据库迁移文件
│   └── seed.ts                       # 数据库种子文件
├── public/                           # 静态资源
│   ├── images/                       # 图片资源
│   ├── icons/                        # 图标文件
│   └── uploads/                      # 用户上传文件
├── package.json                      # 项目依赖和脚本
├── next.config.js                    # Next.js配置
├── tsconfig.json                     # TypeScript配置
├── tailwind.config.ts                # Tailwind CSS配置
├── postcss.config.js                # PostCSS配置
├── .eslintrc.json                    # ESLint配置
├── .gitignore                        # Git忽略文件
├── env.example                       # 环境变量模板
└── README.md                         # 项目说明文档
```

## 核心技术栈

### 前端框架

- **Next.js 15**: 基于React的全栈框架，使用App Router
- **React 19**: 前端UI库
- **TypeScript**: 静态类型检查

### 样式和UI

- **Tailwind CSS**: 原子化CSS框架
- **shadcn/ui**: 基于Radix UI的组件库
- **Lucide React**: 图标库

### 数据库和ORM

- **PostgreSQL**: 主数据库
- **Prisma**: ORM和数据库工具
- **Redis**: 缓存和会话存储(可选)

### 认证

- **NextAuth.js v5**: 认证解决方案
- **bcryptjs**: 密码加密
- **JWT**: 令牌认证

### 开发工具

- **ESLint**: 代码检查
- **Prettier**: 代码格式化
- **tsx**: TypeScript执行器

## 实现状态 (更新至2025年1月7日)

### 已完成 ✅

- 基础项目架构
- 所有核心页面静态版本 (20+页面)
- 组件库和设计系统 (shadcn/ui)
- 响应式布局
- 优化的导航系统
- 样式系统和可访问性改进
- **完整的页面体系**：
  - 企业信息相关页面 (详情、评价、薪资、面试)
  - 用户中心完整功能 (个人资料、消息、收藏、工作经验)
  - 论坛社区功能页面
  - 全局搜索结果页面
  - 完整的静态页面 (帮助中心、关于我们、隐私政策、服务条款)
- TypeScript 类型系统
- 表单验证和错误处理
- 文件上传组件
- **用户认证完整流程** ✅ - NextAuth.js v5 + Google OAuth完整集成
- **数据库部署和连接** ✅ - Supabase + Prisma完全配置并测试通过
- **核心后端API实现** ✅ - 企业信息、评价、薪资、面试API全部完成验证

### 进行中 ⏳

- 前端页面连接到真实API (企业模块已完成85%)

- **论坛系统API完整实现** ✅ - 帖子CRUD、评论、嵌套回复功能已完成
- **用户资料管理API完整实现** ✅ - 个人信息、工作经历、设置管理已完成
- **文件上传系统完整实现** ✅ - 头像、工作文件上传，完整文件管理已完成
- **搜索系统API完整实现** ✅ - 企业、帖子、用户、全局搜索已完成

### 待实施 📋

- **内容审核管理系统** (管理员功能、举报处理)
- **用户统计API** (用户公开统计数据)
- **高级功能** (推荐系统、数据统计、通知系统)
- 性能优化和SEO
- 数据可视化图表集成

### 📊 当前完成度评估

**整体完成度**: 约**98%** (前端完整，所有核心API已完成，包括论坛系统、用户资料管理、文件上传系统和搜索系统)

## 目录详细说明

### 1. src/app/ - 应用路由

使用Next.js 15的App Router，文件系统路由：

#### 路由组 (Route Groups)

- `(auth)/`: 认证相关页面，使用括号创建路由组，不影响URL路径
- 包含登录、注册等页面

#### 动态路由

- `companies/[id]/`: 企业详情页，使用动态路由参数
- `companies/interviews/[id]/`: 面试经验详情页
- `companies/[id]/reviews/`: 企业评价列表页
- `forum/[id]/`: 帖子详情页

#### 功能页面

- `search/`: 全局搜索结果页面，支持企业、职位、面经、帖子的统一搜索
- `companies/salaries/submit/`: 薪资数据提交页面，支持匿名薪资分享
- `companies/interviews/submit/`: 面试经验提交页面，多步骤表单
- `companies/[id]/reviews/submit/`: 企业评价提交页面
- `profile/messages/`: 用户消息中心，支持系统消息和私信
- `profile/favorites/`: 用户收藏夹，管理收藏的企业、面经等内容
- `profile/work-experience/`: 工作经验管理，支持文件上传

#### 静态页面

- `help/`: 帮助中心，包含FAQ、使用指南、联系方式
- `about/`: 关于我们，公司介绍、团队信息、发展历程
- `privacy/`: 隐私政策，详细的数据保护说明
- `terms/`: 服务条款，平台使用规则和法律条款

#### API路由

- `api/`: 后端API endpoints
- 按功能模块组织API路由

### 2. src/components/ - 组件库

#### ui/ - 基础UI组件

基于shadcn/ui的可复用组件：

- `button.tsx`: 按钮组件，支持多种变体
- `card.tsx`: 卡片组件，用于内容展示
- `input.tsx`: 输入框组件
- `dialog.tsx`: 模态对话框
- `badge.tsx`: 标签组件
- `avatar.tsx`: 头像组件

#### forms/ - 表单组件

业务特定的表单组件：

- `auth-form.tsx`: 登录/注册表单
- `company-form.tsx`: 企业信息表单
- `post-form.tsx`: 发帖表单

#### layout/ - 布局组件

页面布局相关组件：

- `header.tsx`: 页面头部，包含导航和用户信息
  - 重新设计的导航结构，避免功能重复
  - 支持响应式设计，PC端hover下拉菜单，移动端点击展开
  - 三大功能区域：企业查询、薪资面经、社区论坛
  - 集成搜索对话框功能
- `footer.tsx`: 页面脚部
- `sidebar.tsx`: 侧边栏，用于论坛等页面
- `navigation.tsx`: 主导航组件（已整合到header中）

#### features/ - 功能模块组件

按功能模块组织的业务组件：

- `companies/`: 企业相关组件(企业卡片、评分组件等)
- `forum/`: 论坛相关组件(帖子列表、评论组件等)
- `salaries/`: 薪资相关组件(薪资表格、统计图等)
- `users/`: 用户相关组件(用户卡片、个人资料等)

### 3. src/lib/ - 工具库

#### 核心配置

- `auth.ts`: NextAuth.js配置，定义认证策略和回调
- `db.ts`: Prisma客户端配置，数据库连接
- `utils.ts`: 通用工具函数(cn、formatDate等)

#### 数据验证

- `validations.ts`: Zod schema定义，用于表单和API验证

#### 自定义Hooks

- `hooks/use-auth.ts`: 认证状态管理Hook
- `hooks/use-companies.ts`: 企业数据获取Hook
- `hooks/use-posts.ts`: 帖子数据管理Hook

### 4. src/types/ - 类型定义

TypeScript类型定义，按功能模块分组：

- `auth.ts`: 认证相关类型
- `company.ts`: 企业相关类型
- `post.ts`: 帖子相关类型
- `user.ts`: 用户相关类型
- `api.ts`: API请求/响应类型

### 5. prisma/ - 数据库配置

- `schema.prisma`: 数据库模式定义
- `migrations/`: 数据库迁移历史
- `seed.ts`: 初始数据种子文件

## 开发规范

### 1. 命名规范

#### 文件命名

- 组件文件：使用kebab-case，如`user-profile.tsx`
- 页面文件：使用page.tsx、layout.tsx等Next.js约定
- 工具文件：使用kebab-case，如`auth-utils.ts`

#### 组件命名

- React组件：使用PascalCase，如`UserProfile`
- Hook：使用camelCase，以use开头，如`useAuth`
- 常量：使用UPPER_SNAKE_CASE，如`DEFAULT_PAGE_SIZE`

### 2. 文件组织

#### 组件结构

```typescript
// components/features/companies/company-card.tsx
import { Company } from '@/types/company'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

interface CompanyCardProps {
  company: Company
}

export function CompanyCard({ company }: CompanyCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{company.name}</CardTitle>
      </CardHeader>
      <CardContent>
        {/* 组件内容 */}
      </CardContent>
    </Card>
  )
}
```

#### API路由结构

```typescript
// app/api/companies/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions)

  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  // API逻辑
}
```

### 3. 状态管理

#### 服务器状态

使用React Query(TanStack Query)管理服务器状态：

```typescript
// hooks/use-companies.ts
import { useQuery } from '@tanstack/react-query'

export function useCompanies() {
  return useQuery({
    queryKey: ['companies'],
    queryFn: () => fetch('/api/companies').then(res => res.json()),
  })
}
```

#### 客户端状态

使用React内置状态管理：

- useState: 局部状态
- useContext: 跨组件状态共享
- useReducer: 复杂状态逻辑

### 4. 样式规范

#### Tailwind CSS使用

- 优先使用Tailwind原子类
- 复杂样式使用组件级CSS
- 使用CSS变量定义主题色彩

#### 响应式设计

```typescript
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
  {/* 响应式网格布局 */}
</div>
```

### 5. 错误处理

#### API错误处理

```typescript
export async function GET() {
  try {
    // API逻辑
  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    )
  }
}
```

#### 客户端错误边界

```typescript
// components/error-boundary.tsx
'use client'

import { Component, ReactNode } from 'react'

interface Props {
  children: ReactNode
}

interface State {
  hasError: boolean
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(): State {
    return { hasError: true }
  }

  render() {
    if (this.state.hasError) {
      return <div>Something went wrong.</div>
    }

    return this.props.children
  }
}
```

## 部署配置

### 环境变量

参考`env.example`文件配置以下环境变量：

- `DATABASE_URL`: PostgreSQL数据库连接字符串
- `NEXTAUTH_SECRET`: NextAuth.js密钥
- `NEXTAUTH_URL`: 应用URL
- OAuth提供商配置等

### 构建脚本

```json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "type-check": "tsc --noEmit",
    "db:push": "prisma db push",
    "db:migrate": "prisma migrate dev",
    "db:generate": "prisma generate"
  }
}
```

### Vercel部署

项目配置为Vercel部署，支持：

- 自动构建和部署
- 环境变量管理
- 数据库集成
- 边缘函数优化

## 开发流程

### 1. 本地开发环境搭建

```bash
# 克隆项目
git clone <repository-url>
cd workmates

# 安装依赖
npm install

# 配置环境变量
cp env.example .env.local

# 初始化数据库
npm run db:push
npm run db:seed

# 启动开发服务器
npm run dev
```

### 2. 开发工作流

1. 创建功能分支
2. 编写代码和测试
3. 运行lint和类型检查
4. 提交PR进行代码审查
5. 合并到主分支
6. 自动部署到生产环境

### 3. 数据库变更流程

```bash
# 修改schema.prisma后
npm run db:migrate
npm run db:generate
```

这个项目结构遵循Next.js 15的最佳实践，具有良好的可维护性和扩展性，适合团队开发和长期维护。
