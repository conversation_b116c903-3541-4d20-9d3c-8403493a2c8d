// 测试Supabase配置和连接的脚本
require('dotenv').config({ path: '.env.local' })

console.log('🚀 WorkMates Supabase 连接测试')
console.log('=' .repeat(50))

// 1. 检查环境变量
console.log('\n📋 1. 检查环境变量配置:')
const requiredVars = [
  'NEXT_PUBLIC_SUPABASE_URL',
  'NEXT_PUBLIC_SUPABASE_ANON_KEY',
  'DATABASE_URL',
  'DIRECT_URL'
]

let allVarsPresent = true
requiredVars.forEach(varName => {
  const value = process.env[varName]
  if (value) {
    // 只显示前10个字符，保护敏感信息
    const maskedValue = value.substring(0, 20) + '...'
    console.log(`  ✅ ${varName}: ${maskedValue}`)
  } else {
    console.log(`  ❌ ${varName}: 未设置`)
    allVarsPresent = false
  }
})

if (!allVarsPresent) {
  console.log('\n❌ 环境变量配置不完整，请检查 .env.local 文件')
  process.exit(1)
}

// 2. 测试Prisma连接
console.log('\n📊 2. 测试Prisma数据库连接:')
async function testPrismaConnection() {
  try {
    const { PrismaClient } = require('@prisma/client')
    const prisma = new PrismaClient()
    
    // 测试数据库连接
    await prisma.$connect()
    console.log('  ✅ Prisma数据库连接成功')
    
    // 测试简单查询（如果表不存在会报错，这是正常的）
    try {
      await prisma.$queryRaw`SELECT 1 as test`
      console.log('  ✅ 数据库查询测试成功')
    } catch (queryError) {
      console.log('  ℹ️ 数据库查询测试:', queryError.message)
    }
    
    await prisma.$disconnect()
    return true
  } catch (error) {
    console.log('  ❌ Prisma连接失败:', error.message)
    return false
  }
}

// 3. 项目配置信息
console.log('\n📝 3. 项目配置信息:')
console.log(`  📊 项目名称: WorkMates`)
console.log(`  🌐 Supabase项目ID: zfctpeukxaxftfsmpqgp`)
console.log(`  🏠 本地端口: 3000`)
console.log(`  📦 Node.js版本: ${process.version}`)

// 运行测试
async function runTests() {
  console.log('\n🧪 运行连接测试...')
  
  const prismaTest = await testPrismaConnection()
  
  console.log('\n' + '='.repeat(50))
  if (prismaTest) {
    console.log('🎉 Supabase配置测试完成！')
    console.log('📝 下一步操作:')
    console.log('  1. 运行 `npm run dev` 启动开发服务器')
    console.log('  2. 运行 `npx prisma db push` 同步数据库结构')
    console.log('  3. 运行 `npx prisma studio` 查看数据库')
  } else {
    console.log('❌ 测试失败，请检查配置')
  }
}

runTests().catch(console.error) 