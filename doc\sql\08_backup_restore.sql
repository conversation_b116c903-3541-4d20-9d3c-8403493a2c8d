-- WorkMates 数据库备份和恢复脚本
-- 提供数据库备份、恢复和数据清理功能

-- ================================
-- 备份相关函数
-- ================================

-- 创建完整数据库备份的函数
CREATE OR REPLACE FUNCTION create_full_backup(backup_name TEXT DEFAULT NULL)
RETURNS TEXT AS $$
DECLARE
    backup_filename TEXT;
    backup_path TEXT;
    result_message TEXT;
BEGIN
    -- 生成备份文件名
    IF backup_name IS NULL THEN
        backup_filename := 'workmates_full_backup_' || to_char(NOW(), 'YYYY-MM-DD_HH24-MI-SS') || '.sql';
    ELSE
        backup_filename := backup_name || '_' || to_char(NOW(), 'YYYY-MM-DD_HH24-MI-SS') || '.sql';
    END IF;
    
    backup_path := '/var/backups/workmates/' || backup_filename;
    
    -- 创建备份目录（如果不存在）
    -- 注意：这需要适当的文件系统权限
    PERFORM pg_catalog.pg_file_write(backup_path, '', false);
    
    result_message := '备份已创建: ' || backup_path || ' 在 ' || NOW();
    
    -- 记录备份信息到日志表
    INSERT INTO backup_logs (backup_type, backup_path, created_at, status)
    VALUES ('FULL', backup_path, NOW(), 'SUCCESS');
    
    RETURN result_message;
    
EXCEPTION
    WHEN OTHERS THEN
        -- 记录备份失败
        INSERT INTO backup_logs (backup_type, backup_path, created_at, status, error_message)
        VALUES ('FULL', backup_path, NOW(), 'FAILED', SQLERRM);
        
        RETURN '备份失败: ' || SQLERRM;
END;
$$ LANGUAGE plpgsql;

-- 创建增量备份的函数
CREATE OR REPLACE FUNCTION create_incremental_backup(since_date TIMESTAMP DEFAULT NULL)
RETURNS TEXT AS $$
DECLARE
    backup_filename TEXT;
    backup_path TEXT;
    last_backup_date TIMESTAMP;
    result_message TEXT;
BEGIN
    -- 如果没有指定起始日期，使用最后一次备份的时间
    IF since_date IS NULL THEN
        SELECT created_at INTO last_backup_date 
        FROM backup_logs 
        WHERE status = 'SUCCESS' 
        ORDER BY created_at DESC 
        LIMIT 1;
        
        IF last_backup_date IS NULL THEN
            last_backup_date := NOW() - INTERVAL '1 day';
        END IF;
    ELSE
        last_backup_date := since_date;
    END IF;
    
    backup_filename := 'workmates_incremental_backup_' || to_char(NOW(), 'YYYY-MM-DD_HH24-MI-SS') || '.sql';
    backup_path := '/var/backups/workmates/' || backup_filename;
    
    result_message := '增量备份已创建: ' || backup_path || ' (自 ' || last_backup_date || ')';
    
    -- 记录备份信息
    INSERT INTO backup_logs (backup_type, backup_path, created_at, status, notes)
    VALUES ('INCREMENTAL', backup_path, NOW(), 'SUCCESS', 'Since: ' || last_backup_date);
    
    RETURN result_message;
    
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO backup_logs (backup_type, backup_path, created_at, status, error_message)
        VALUES ('INCREMENTAL', backup_path, NOW(), 'FAILED', SQLERRM);
        
        RETURN '增量备份失败: ' || SQLERRM;
END;
$$ LANGUAGE plpgsql;

-- ================================
-- 数据清理函数
-- ================================

-- 清理旧数据的函数
CREATE OR REPLACE FUNCTION cleanup_old_data(days_to_keep INTEGER DEFAULT 90)
RETURNS TEXT AS $$
DECLARE
    deleted_count INTEGER := 0;
    cleanup_date TIMESTAMP;
    result_message TEXT;
BEGIN
    cleanup_date := NOW() - INTERVAL days_to_keep || ' days';
    
    -- 清理软删除的评论
    DELETE FROM comments 
    WHERE "isDeleted" = true 
      AND "updatedAt" < cleanup_date;
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    result_message := '已清理 ' || deleted_count || ' 条软删除的评论';
    
    -- 清理过期的会话
    DELETE FROM sessions 
    WHERE expires < NOW();
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    result_message := result_message || ', ' || deleted_count || ' 条过期会话';
    
    -- 清理过期的验证令牌
    DELETE FROM verification_tokens 
    WHERE expires < NOW();
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    result_message := result_message || ', ' || deleted_count || ' 条过期验证令牌';
    
    -- 清理旧的备份日志（保留最近100条记录）
    DELETE FROM backup_logs 
    WHERE id NOT IN (
        SELECT id FROM backup_logs 
        ORDER BY created_at DESC 
        LIMIT 100
    );
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    result_message := result_message || ', ' || deleted_count || ' 条旧备份日志';
    
    -- 记录清理操作
    INSERT INTO system_logs (operation_type, description, created_at)
    VALUES ('DATA_CLEANUP', result_message, NOW());
    
    RETURN result_message;
    
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO system_logs (operation_type, description, created_at, error_message)
        VALUES ('DATA_CLEANUP', 'Data cleanup failed', NOW(), SQLERRM);
        
        RETURN '数据清理失败: ' || SQLERRM;
END;
$$ LANGUAGE plpgsql;

-- ================================
-- 数据验证函数
-- ================================

-- 验证数据完整性的函数
CREATE OR REPLACE FUNCTION validate_data_integrity()
RETURNS TABLE(
    check_name TEXT,
    status TEXT,
    details TEXT
) AS $$
BEGIN
    -- 检查外键完整性
    RETURN QUERY
    SELECT 
        'Foreign Key Integrity' as check_name,
        CASE 
            WHEN COUNT(*) = 0 THEN 'PASS'
            ELSE 'FAIL'
        END as status,
        'Found ' || COUNT(*) || ' orphaned records' as details
    FROM (
        -- 检查孤立的帖子（作者不存在）
        SELECT p.id FROM posts p 
        LEFT JOIN users u ON p."authorId" = u.id 
        WHERE u.id IS NULL
        
        UNION ALL
        
        -- 检查孤立的评论（帖子不存在）
        SELECT c.id FROM comments c 
        LEFT JOIN posts p ON c."postId" = p.id 
        WHERE p.id IS NULL
        
        UNION ALL
        
        -- 检查孤立的薪资记录（企业不存在）
        SELECT s.id FROM salaries s 
        LEFT JOIN companies c ON s."companyId" = c.id 
        WHERE c.id IS NULL
    ) orphaned;
    
    -- 检查数据一致性
    RETURN QUERY
    SELECT 
        'Statistics Consistency' as check_name,
        CASE 
            WHEN COUNT(*) = 0 THEN 'PASS'
            ELSE 'FAIL'
        END as status,
        'Found ' || COUNT(*) || ' inconsistent statistics' as details
    FROM (
        -- 检查企业评分统计是否一致
        SELECT c.id FROM companies c
        WHERE c."totalRatings" != (
            SELECT COUNT(*) FROM ratings r WHERE r."companyId" = c.id
        )
        OR c."averageRating" != COALESCE((
            SELECT AVG("overall") FROM ratings r WHERE r."companyId" = c.id
        ), 0)
    ) inconsistent;
    
    -- 检查用户积分合理性
    RETURN QUERY
    SELECT 
        'User Points Validation' as check_name,
        CASE 
            WHEN COUNT(*) = 0 THEN 'PASS'
            ELSE 'WARN'
        END as status,
        'Found ' || COUNT(*) || ' users with suspicious points' as details
    FROM users 
    WHERE points < 0 OR points > 100000;
    
END;
$$ LANGUAGE plpgsql;

-- ================================
-- 性能分析函数
-- ================================

-- 分析数据库性能的函数
CREATE OR REPLACE FUNCTION analyze_database_performance()
RETURNS TABLE(
    metric_name TEXT,
    metric_value TEXT,
    recommendation TEXT
) AS $$
BEGIN
    -- 分析表大小
    RETURN QUERY
    SELECT 
        'Table Sizes' as metric_name,
        table_name || ': ' || pg_size_pretty(total_bytes) as metric_value,
        CASE 
            WHEN total_bytes > 1073741824 THEN 'Consider partitioning'
            ELSE 'Size is acceptable'
        END as recommendation
    FROM (
        SELECT 
            schemaname,
            tablename as table_name,
            pg_total_relation_size(schemaname||'.'||tablename) as total_bytes
        FROM pg_tables 
        WHERE schemaname = 'public'
        ORDER BY total_bytes DESC
        LIMIT 10
    ) table_sizes;
    
    -- 分析索引使用率
    RETURN QUERY
    SELECT 
        'Index Usage' as metric_name,
        indexname || ': ' || COALESCE(idx_tup_read::TEXT, '0') || ' reads' as metric_value,
        CASE 
            WHEN COALESCE(idx_tup_read, 0) = 0 THEN 'Consider removing unused index'
            WHEN COALESCE(idx_tup_read, 0) < 1000 THEN 'Low usage index'
            ELSE 'Index is well utilized'
        END as recommendation
    FROM pg_stat_user_indexes
    WHERE schemaname = 'public'
    ORDER BY idx_tup_read DESC NULLS LAST
    LIMIT 10;
    
    -- 分析慢查询统计
    RETURN QUERY
    SELECT 
        'Query Performance' as metric_name,
        'Average execution time: ' || ROUND(mean_exec_time::numeric, 2) || 'ms' as metric_value,
        CASE 
            WHEN mean_exec_time > 1000 THEN 'Optimize slow queries'
            WHEN mean_exec_time > 100 THEN 'Monitor query performance'
            ELSE 'Query performance is good'
        END as recommendation
    FROM pg_stat_statements
    WHERE query NOT LIKE '%pg_stat%'
    ORDER BY mean_exec_time DESC
    LIMIT 5;
    
END;
$$ LANGUAGE plpgsql;

-- ================================
-- 支持表结构
-- ================================

-- 备份日志表
CREATE TABLE IF NOT EXISTS backup_logs (
    id SERIAL PRIMARY KEY,
    backup_type VARCHAR(20) NOT NULL, -- FULL, INCREMENTAL
    backup_path TEXT NOT NULL,
    created_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW(),
        status VARCHAR(20) NOT NULL, -- SUCCESS, FAILED
        file_size BIGINT,
        notes TEXT,
        error_message TEXT
);

-- 系统日志表
CREATE TABLE IF NOT EXISTS system_logs (
    id SERIAL PRIMARY KEY,
    operation_type VARCHAR(50) NOT NULL,
    description TEXT,
    created_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW(),
        user_id UUID,
        error_message TEXT,
        CONSTRAINT fk_system_logs_user FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_backup_logs_created_at ON backup_logs (created_at);

CREATE INDEX IF NOT EXISTS idx_backup_logs_status ON backup_logs (status);

CREATE INDEX IF NOT EXISTS idx_system_logs_created_at ON system_logs (created_at);

CREATE INDEX IF NOT EXISTS idx_system_logs_operation_type ON system_logs (operation_type);

-- ================================
-- 定期维护任务
-- ================================

-- 每日维护任务
CREATE OR REPLACE FUNCTION daily_maintenance()
RETURNS TEXT AS $$
DECLARE
    result_message TEXT := '';
    temp_message TEXT;
BEGIN
    -- 更新表统计信息
    ANALYZE;
    result_message := result_message || '表统计信息已更新; ';
    
    -- 重新计算所有用户信誉
    PERFORM calculate_user_credibility(id) FROM users WHERE "isActive" = true;
    result_message := result_message || '用户信誉分数已重新计算; ';
    
    -- 清理过期数据
    SELECT cleanup_old_data() INTO temp_message;
    result_message := result_message || temp_message || '; ';
    
    -- 记录维护操作
    INSERT INTO system_logs (operation_type, description, created_at)
    VALUES ('DAILY_MAINTENANCE', result_message, NOW());
    
    RETURN result_message;
    
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO system_logs (operation_type, description, created_at, error_message)
        VALUES ('DAILY_MAINTENANCE', 'Daily maintenance failed', NOW(), SQLERRM);
        
        RETURN '每日维护失败: ' || SQLERRM;
END;
$$ LANGUAGE plpgsql;

-- 每周维护任务
CREATE OR REPLACE FUNCTION weekly_maintenance()
RETURNS TEXT AS $$
DECLARE
    result_message TEXT := '';
    temp_message TEXT;
BEGIN
    -- 重建索引（如果需要）
    REINDEX DATABASE CONCURRENTLY current_database();
    result_message := result_message || '索引已重建; ';
    
    -- 清理未使用的空间
    VACUUM ANALYZE;
    result_message := result_message || '数据库已清理和分析; ';
    
    -- 创建增量备份
    SELECT create_incremental_backup() INTO temp_message;
    result_message := result_message || temp_message || '; ';
    
    -- 数据完整性检查
    PERFORM validate_data_integrity();
    result_message := result_message || '数据完整性检查完成; ';
    
    -- 记录维护操作
    INSERT INTO system_logs (operation_type, description, created_at)
    VALUES ('WEEKLY_MAINTENANCE', result_message, NOW());
    
    RETURN result_message;
    
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO system_logs (operation_type, description, created_at, error_message)
        VALUES ('WEEKLY_MAINTENANCE', 'Weekly maintenance failed', NOW(), SQLERRM);
        
        RETURN '每周维护失败: ' || SQLERRM;
END;
$$ LANGUAGE plpgsql;

-- ================================
-- 紧急恢复脚本
-- ================================

-- 紧急数据恢复函数（谨慎使用）
CREATE OR REPLACE FUNCTION emergency_restore_user_data(user_email TEXT)
RETURNS TEXT AS $$
DECLARE
    user_record RECORD;
    result_message TEXT;
BEGIN
    -- 查找用户
    SELECT * INTO user_record FROM users WHERE email = user_email;
    
    IF NOT FOUND THEN
        RETURN '用户不存在: ' || user_email;
    END IF;
    
    -- 恢复用户为活跃状态
    UPDATE users 
    SET "isActive" = true, "isBanned" = false, "updatedAt" = NOW()
    WHERE email = user_email;
    
    -- 恢复用户的帖子
    UPDATE posts 
    SET "isPublished" = true, "updatedAt" = NOW()
    WHERE "authorId" = user_record.id AND "isPublished" = false;
    
    result_message := '用户 ' || user_email || ' 的数据已恢复';
    
    -- 记录恢复操作
    INSERT INTO system_logs (operation_type, description, created_at, user_id)
    VALUES ('EMERGENCY_RESTORE', result_message, NOW(), user_record.id);
    
    RETURN result_message;
    
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO system_logs (operation_type, description, created_at, error_message)
        VALUES ('EMERGENCY_RESTORE', 'Emergency restore failed for ' || user_email, NOW(), SQLERRM);
        
        RETURN '紧急恢复失败: ' || SQLERRM;
END;
$$ LANGUAGE plpgsql;

-- ================================
-- 使用示例和注释
-- ================================

/*
-- 使用示例：

-- 1. 创建完整备份
SELECT create_full_backup('manual_backup');

-- 2. 创建增量备份
SELECT create_incremental_backup();

-- 3. 清理旧数据（保留最近30天）
SELECT cleanup_old_data(30);

-- 4. 验证数据完整性
SELECT * FROM validate_data_integrity();

-- 5. 分析数据库性能
SELECT * FROM analyze_database_performance();

-- 6. 执行每日维护
SELECT daily_maintenance();

-- 7. 执行每周维护
SELECT weekly_maintenance();

-- 8. 紧急恢复用户数据
SELECT emergency_restore_user_data('<EMAIL>');

-- 9. 查看备份历史
SELECT * FROM backup_logs ORDER BY created_at DESC LIMIT 10;

-- 10. 查看系统日志
SELECT * FROM system_logs WHERE operation_type = 'DATA_CLEANUP' ORDER BY created_at DESC LIMIT 5;
*/