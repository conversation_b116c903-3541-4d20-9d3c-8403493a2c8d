-- WorkMates 枚举类型和自定义类型定义
-- 根据 Prisma Schema 生成

-- ================================
-- 枚举类型定义
-- ================================

-- 用户等级枚举
CREATE TYPE "UserLevel" AS ENUM (
  'NEWBIE',     -- 新手
  'ACTIVE',     -- 活跃用户
  'SENIOR',     -- 资深用户
  'EXPERT',     -- 专家
  'MODERATOR',  -- 版主
  'ADMIN'       -- 管理员
);

-- 企业规模枚举
CREATE TYPE "CompanySize" AS ENUM (
  'STARTUP',    -- 创业公司 (1-50人)
  'SMALL',      -- 小型公司 (51-200人)
  'MEDIUM',     -- 中型公司 (201-1000人)
  'LARGE',      -- 大型公司 (1001-5000人)
  'ENTERPRISE'  -- 超大型企业 (5000+人)
);

-- 帖子类型枚举
CREATE TYPE "PostType" AS ENUM (
  'DISCUSSION', -- 讨论
  'QUESTION',   -- 问题
  'SHARING',    -- 分享
  'NEWS',       -- 新闻
  'REVIEW',     -- 评价
  'JOB'         -- 招聘
);

-- 工作类型枚举已合并到EmploymentType，避免重复定义

-- 面试难度枚举
CREATE TYPE "InterviewDifficulty" AS ENUM (
  'EASY',
  'MEDIUM',
  'HARD',
  'VERY_HARD'
);

-- 面试结果枚举
CREATE TYPE "InterviewResult" AS ENUM (
  'PASSED',     -- 通过
  'FAILED',     -- 未通过
  'PENDING',    -- 等待结果
  'CANCELLED'   -- 取消
);

-- 举报原因枚举
CREATE TYPE "ReportReason" AS ENUM (
  'SPAM',         -- 垃圾信息
  'INAPPROPRIATE', -- 不当内容
  'FAKE_INFO',    -- 虚假信息
  'HARASSMENT',   -- 骚扰
  'COPYRIGHT',    -- 版权侵犯
  'OTHER'         -- 其他
);

-- 举报状态枚举
CREATE TYPE "ReportStatus" AS ENUM (
  'PENDING',    -- 待处理
  'REVIEWING',  -- 审核中
  'RESOLVED',   -- 已解决
  'REJECTED'    -- 已拒绝
);

-- ================================
-- 工作经历管理相关枚举
-- ================================

-- 雇佣类型枚举
CREATE TYPE "EmploymentType" AS ENUM (
  'FULL_TIME',  -- 全职
  'PART_TIME',  -- 兼职
  'CONTRACT',   -- 合同工
  'INTERNSHIP', -- 实习
  'FREELANCE'   -- 自由职业
);

-- 审核状态枚举
CREATE TYPE "VerificationStatus" AS ENUM (
  'PENDING',    -- 待审核
  'APPROVED',   -- 已通过
  'REJECTED',   -- 已拒绝
  'REVOKED'     -- 已撤销
);

-- 文件类型枚举
CREATE TYPE "FileCategory" AS ENUM (
  'CONTRACT',   -- 合同
  'CERTIFICATE',-- 证书
  'PHOTO',      -- 照片
  'DOCUMENT',   -- 文档
  'OTHER'       -- 其他
);

-- 审核目标类型枚举
CREATE TYPE "TargetType" AS ENUM (
  'WORK_EXPERIENCE', -- 工作经历
  'EXPERIENCE_FILE', -- 经历文件
  'SALARY',         -- 薪资信息
  'INTERVIEW'       -- 面试经历
);

-- 审核决定枚举
CREATE TYPE "ReviewDecision" AS ENUM (
  'APPROVED',        -- 通过
  'REJECTED',        -- 拒绝
  'REVOKED',         -- 撤销
  'PENDING_MORE_INFO' -- 需要更多信息
);

-- ================================
-- 自定义函数类型
-- ================================

-- UUID生成函数 (确保兼容性)
CREATE OR REPLACE FUNCTION generate_uuid() RETURNS UUID AS $$
BEGIN
  RETURN gen_random_uuid();
END;
$$ LANGUAGE plpgsql;

-- 更新时间戳函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;