/** @type {import('next').NextConfig} */
const nextConfig = {
  serverExternalPackages: ['@prisma/client', 'bcryptjs'],

  // ===== 图片配置 =====
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
  },

  // ===== 调试和开发配置 =====
  experimental: {
    // 改善开发体验
    optimizePackageImports: ['@radix-ui/react-icons', 'lucide-react'],
  },

  // ===== TypeScript 配置 =====
  typescript: {
    // 确保类型安全，不忽略构建错误
    ignoreBuildErrors: false,
  },

  // ===== ESLint 配置 =====
  eslint: {
    // 构建时不忽略ESLint错误，确保代码质量
    ignoreDuringBuilds: false,
  },

  // ===== 开发环境配置 =====
  ...(process.env.NODE_ENV === 'development' && {
    // 开发服务器配置 - 使用更保守的设置
    devIndicators: {
      position: 'bottom-right',
    },

    // 启用严格模式，帮助发现潜在问题
    reactStrictMode: true,

    // 开发环境下的性能指标
    poweredByHeader: false,
  }),

  // ===== 生产环境优化 =====
  ...(process.env.NODE_ENV === 'production' && {
    // 生产环境下禁用源映射以提升性能
    productionBrowserSourceMaps: false,

    // 启用压缩
    compress: true,

    // 严格模式
    reactStrictMode: true,

    // 移除调试信息
    poweredByHeader: false,
  }),
}

module.exports = nextConfig
