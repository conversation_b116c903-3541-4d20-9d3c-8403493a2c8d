-- WorkMates 数据库索引创建脚本
-- 优化数据库查询性能

-- ================================
-- 用户表索引
-- ================================

-- 用户邮箱唯一索引 (Prisma已创建)
-- CREATE UNIQUE INDEX IF NOT EXISTS idx_users_email ON users(email);

-- 用户名唯一索引 (Prisma已创建)
-- CREATE UNIQUE INDEX IF NOT EXISTS idx_users_username ON users(username);

-- 用户手机号索引
CREATE INDEX IF NOT EXISTS idx_users_phone ON users (phone)
WHERE
    phone IS NOT NULL;

-- 用户等级索引 (用于用户权限查询)
CREATE INDEX IF NOT EXISTS idx_users_level ON users (level);

-- 用户状态复合索引
CREATE INDEX IF NOT EXISTS idx_users_status ON users (
    is_active,
    is_banned,
    is_verified
);

-- 用户创建时间索引 (用于统计分析)
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users (created_at);

-- 用户行业和职位索引 (用于用户匹配)
CREATE INDEX IF NOT EXISTS idx_users_industry_position ON users (industry, position)
WHERE
    industry IS NOT NULL
    AND position IS NOT NULL;

-- ================================
-- 企业表索引
-- ================================

-- 企业名称索引 (支持模糊搜索)
CREATE INDEX IF NOT EXISTS idx_companies_name ON companies USING gin (to_tsvector ('simple', name));

CREATE INDEX IF NOT EXISTS idx_companies_name_en ON companies USING gin (
    to_tsvector ('english', name_en)
)
WHERE
    name_en IS NOT NULL;

-- 企业行业索引
CREATE INDEX IF NOT EXISTS idx_companies_industry ON companies (industry)
WHERE
    industry IS NOT NULL;

-- 企业规模索引
CREATE INDEX IF NOT EXISTS idx_companies_size ON companies (size)
WHERE
    size IS NOT NULL;

-- 企业状态索引
CREATE INDEX IF NOT EXISTS idx_companies_status ON companies (is_verified, is_active);

-- 企业评分索引 (用于排序)
CREATE INDEX IF NOT EXISTS idx_companies_rating ON companies (
    average_rating DESC,
    total_ratings DESC
);

-- 企业地区索引
CREATE INDEX IF NOT EXISTS idx_companies_headquarters ON companies (headquarters)
WHERE
    headquarters IS NOT NULL;

-- ================================
-- 帖子表索引
-- ================================

-- 帖子作者索引
CREATE INDEX IF NOT EXISTS idx_posts_author_id ON posts (author_id);

-- 帖子企业关联索引
CREATE INDEX IF NOT EXISTS idx_posts_company_id ON posts (company_id)
WHERE
    company_id IS NOT NULL;

-- 帖子类型索引
CREATE INDEX IF NOT EXISTS idx_posts_type ON posts(type);

-- 帖子状态复合索引
CREATE INDEX IF NOT EXISTS idx_posts_status ON posts (
    is_published,
    is_pinned,
    created_at DESC
)
WHERE
    is_published = true;

-- 帖子标签索引 (GIN索引支持数组查询)
CREATE INDEX IF NOT EXISTS idx_posts_tags ON posts USING gin (tags);

-- 帖子全文搜索索引
CREATE INDEX IF NOT EXISTS idx_posts_search ON posts USING gin (
    to_tsvector (
        'simple',
        title || ' ' || content
    )
);

-- 帖子热度索引 (综合排序)
CREATE INDEX IF NOT EXISTS idx_posts_popularity ON posts (
    view_count DESC,
    like_count DESC,
    comment_count DESC
);

-- 帖子时间范围索引
CREATE INDEX IF NOT EXISTS idx_posts_time_range ON posts (
    created_at DESC,
    updated_at DESC
)
WHERE
    is_published = true;

-- ================================
-- 评论表索引
-- ================================

-- 评论帖子关联索引
CREATE INDEX IF NOT EXISTS idx_comments_post_id ON comments (post_id);

-- 评论作者索引
CREATE INDEX IF NOT EXISTS idx_comments_author_id ON comments (author_id);

-- 评论回复层级索引
CREATE INDEX IF NOT EXISTS idx_comments_parent_id ON comments (parent_id)
WHERE
    parent_id IS NOT NULL;

-- 评论状态和时间索引
CREATE INDEX IF NOT EXISTS idx_comments_status_time ON comments (is_deleted, created_at DESC)
WHERE
    is_deleted = false;

-- ================================
-- 薪资表索引
-- ================================

-- 薪资企业关联索引
CREATE INDEX IF NOT EXISTS idx_salaries_company_id ON salaries (company_id);

-- 薪资作者索引
CREATE INDEX IF NOT EXISTS idx_salaries_author_id ON salaries (author_id);

-- 薪资职位索引
CREATE INDEX IF NOT EXISTS idx_salaries_position ON salaries (position);

-- 薪资水平索引 (用于统计分析)
CREATE INDEX IF NOT EXISTS idx_salaries_salary_range ON salaries (
    total_salary DESC,
    base_salary DESC
);

-- 薪资工作类型索引
CREATE INDEX IF NOT EXISTS idx_salaries_work_type ON salaries (work_type);

-- 薪资地区索引
CREATE INDEX IF NOT EXISTS idx_salaries_location ON salaries (work_location)
WHERE
    work_location IS NOT NULL;

-- 薪资验证状态索引
CREATE INDEX IF NOT EXISTS idx_salaries_verified ON salaries (is_verified, created_at DESC);

-- 薪资多维度查询索引
CREATE INDEX IF NOT EXISTS idx_salaries_multi ON salaries (
    company_id,
    position,
    work_type,
    is_verified
);

-- ================================
-- 面经表索引
-- ================================

-- 面经企业关联索引
CREATE INDEX IF NOT EXISTS idx_interviews_company_id ON interviews (company_id);

-- 面经作者索引
CREATE INDEX IF NOT EXISTS idx_interviews_author_id ON interviews (author_id);

-- 面经职位索引
CREATE INDEX IF NOT EXISTS idx_interviews_position ON interviews (position);

-- 面经难度索引
CREATE INDEX IF NOT EXISTS idx_interviews_difficulty ON interviews (difficulty)
WHERE
    difficulty IS NOT NULL;

-- 面经结果索引
CREATE INDEX IF NOT EXISTS idx_interviews_result ON interviews (result)
WHERE
    result IS NOT NULL;

-- 面经时间索引
CREATE INDEX IF NOT EXISTS idx_interviews_date ON interviews (
    interview_date DESC,
    created_at DESC
);

-- 面经offer状态索引
CREATE INDEX IF NOT EXISTS idx_interviews_offer ON interviews (offer, offer_salary DESC)
WHERE
    offer = true;

-- ================================
-- 评分表索引
-- ================================

-- 评分企业关联索引
CREATE INDEX IF NOT EXISTS idx_ratings_company_id ON ratings (company_id);

-- 评分作者索引
CREATE INDEX IF NOT EXISTS idx_ratings_author_id ON ratings (author_id);

-- 评分综合分数索引
CREATE INDEX IF NOT EXISTS idx_ratings_overall ON ratings (overall DESC, created_at DESC);

-- 评分验证状态索引
CREATE INDEX IF NOT EXISTS idx_ratings_verified ON ratings (is_verified, created_at DESC);

-- ================================
-- 点赞表索引
-- ================================

-- 点赞用户索引
CREATE INDEX IF NOT EXISTS idx_likes_user_id ON likes (user_id);

-- 点赞帖子索引
CREATE INDEX IF NOT EXISTS idx_likes_post_id ON likes (post_id)
WHERE
    post_id IS NOT NULL;

-- 点赞评论索引
CREATE INDEX IF NOT EXISTS idx_likes_comment_id ON likes (comment_id)
WHERE
    comment_id IS NOT NULL;

-- ================================
-- 收藏表索引
-- ================================

-- 收藏用户索引
CREATE INDEX IF NOT EXISTS idx_bookmarks_user_id ON bookmarks (user_id);

-- 收藏帖子索引
CREATE INDEX IF NOT EXISTS idx_bookmarks_post_id ON bookmarks (post_id);

-- 收藏时间索引
CREATE INDEX IF NOT EXISTS idx_bookmarks_created_at ON bookmarks (created_at DESC);

-- ================================
-- 举报表索引
-- ================================

-- 举报人索引
CREATE INDEX IF NOT EXISTS idx_reports_reporter_id ON reports (reporter_id);

-- 举报状态索引
CREATE INDEX IF NOT EXISTS idx_reports_status ON reports (status, created_at DESC);

-- 举报类型索引
CREATE INDEX IF NOT EXISTS idx_reports_reason ON reports (reason);

-- ================================
-- NextAuth 相关索引
-- ================================

-- Account表索引
CREATE INDEX IF NOT EXISTS idx_accounts_user_id ON accounts (user_id);

CREATE INDEX IF NOT EXISTS idx_accounts_provider ON accounts (provider, provider_account_id);

-- Session表索引
CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON sessions (user_id);

CREATE INDEX IF NOT EXISTS idx_sessions_expires ON sessions (expires);

-- ================================
-- 复合查询索引
-- ================================

-- 用户活跃度分析索引
CREATE INDEX IF NOT EXISTS idx_user_activity ON users (
    last_login DESC,
    points DESC,
    reputation DESC
)
WHERE
    is_active = true;

-- 企业热度排序索引
CREATE INDEX IF NOT EXISTS idx_company_popularity ON companies (
    total_ratings DESC,
    average_rating DESC,
    total_reviews DESC
)
WHERE
    is_active = true;

-- 帖子推荐算法索引
CREATE INDEX IF NOT EXISTS idx_post_recommendation ON posts(type, created_at DESC, like_count DESC) WHERE is_published = true AND is_pinned = false;

-- 薪资数据分析索引
CREATE INDEX IF NOT EXISTS idx_salary_analysis ON salaries (
    company_id,
    position,
    experience,
    total_salary DESC
)
WHERE
    is_verified = true;

-- ================================
-- 部分索引 (Partial Indexes)
-- ================================

-- 只为活跃用户创建索引
CREATE INDEX IF NOT EXISTS idx_active_users_level ON users (level, points DESC)
WHERE
    is_active = true
    AND is_banned = false;

-- 只为已发布帖子创建全文搜索索引
CREATE INDEX IF NOT EXISTS idx_published_posts_search ON posts USING gin (
    to_tsvector (
        'simple',
        title || ' ' || content
    )
)
WHERE
    is_published = true;

-- 只为已验证薪资创建统计索引
CREATE INDEX IF NOT EXISTS idx_verified_salaries_stats ON salaries (
    position,
    total_salary,
    experience
)
WHERE
    is_verified = true;

-- ================================
-- 统计分析相关索引
-- ================================

-- 按月统计用户注册索引
CREATE INDEX IF NOT EXISTS idx_users_monthly_stats ON users (
    date_trunc ('month', created_at)
);

-- 按日统计帖子发布索引
CREATE INDEX IF NOT EXISTS idx_posts_daily_stats ON posts (
    date_trunc ('day', created_at)
)
WHERE
    is_published = true;

-- 企业薪资统计索引
CREATE INDEX IF NOT EXISTS idx_company_salary_stats ON salaries (
    company_id,
    date_trunc ('year', created_at),
    total_salary
);

-- ================================
-- 工作经历管理系统索引
-- ================================

-- 工作经历表索引
CREATE INDEX IF NOT EXISTS idx_work_experiences_user_id ON work_experiences (user_id);

CREATE INDEX IF NOT EXISTS idx_work_experiences_company ON work_experiences (company_name);

CREATE INDEX IF NOT EXISTS idx_work_experiences_position ON work_experiences (position);

CREATE INDEX IF NOT EXISTS idx_work_experiences_verification_status ON work_experiences (verification_status);

CREATE INDEX IF NOT EXISTS idx_work_experiences_date_range ON work_experiences (start_date, end_date);

CREATE INDEX IF NOT EXISTS idx_work_experiences_current ON work_experiences (is_current)
WHERE
    is_current = true;

CREATE INDEX IF NOT EXISTS idx_work_experiences_employment_type ON work_experiences (employment_type);

CREATE INDEX IF NOT EXISTS idx_work_experiences_verified_by ON work_experiences (verified_by_id)
WHERE
    verified_by_id IS NOT NULL;

-- 工作经历文件表索引
CREATE INDEX IF NOT EXISTS idx_experience_files_work_experience_id ON experience_files (work_experience_id);

CREATE INDEX IF NOT EXISTS idx_experience_files_verification_status ON experience_files (verification_status);

CREATE INDEX IF NOT EXISTS idx_experience_files_category ON experience_files (file_category);

CREATE INDEX IF NOT EXISTS idx_experience_files_public ON experience_files (is_public)
WHERE
    is_public = true;

CREATE INDEX IF NOT EXISTS idx_experience_files_verified_by ON experience_files (verified_by_id)
WHERE
    verified_by_id IS NOT NULL;

-- 审核记录表索引
CREATE INDEX IF NOT EXISTS idx_verification_records_target ON verification_records (target_type, target_id);

CREATE INDEX IF NOT EXISTS idx_verification_records_reviewer ON verification_records (reviewer_id);

CREATE INDEX IF NOT EXISTS idx_verification_records_created_at ON verification_records (created_at);

CREATE INDEX IF NOT EXISTS idx_verification_records_decision ON verification_records (decision);

-- 用户信誉表索引
CREATE INDEX IF NOT EXISTS idx_user_credibility_user_id ON user_credibility (user_id);

CREATE INDEX IF NOT EXISTS idx_user_credibility_overall_score ON user_credibility (overall_score DESC);

CREATE INDEX IF NOT EXISTS idx_user_credibility_updated_at ON user_credibility (last_calculated_at);

-- ================================
-- 工作经历管理复合索引
-- ================================

-- 工作经历多维度查询索引
CREATE INDEX IF NOT EXISTS idx_work_experiences_multi ON work_experiences (
    user_id,
    verification_status,
    is_current
);

-- 文件审核工作流索引
CREATE INDEX IF NOT EXISTS idx_experience_files_workflow ON experience_files (
    verification_status,
    uploaded_at DESC
);

-- 用户信誉排名索引
CREATE INDEX IF NOT EXISTS idx_user_credibility_ranking ON user_credibility (
    overall_score DESC,
    total_contributions_count DESC
)
WHERE
    overall_score > 0;

-- 审核工作量统计索引
CREATE INDEX IF NOT EXISTS idx_verification_records_workload ON verification_records (
    reviewer_id,
    date_trunc ('month', created_at),
    decision
);

-- ================================
-- 性能监控索引
-- ================================

-- 慢查询监控相关索引会根据实际运行情况动态添加
-- 使用 EXPLAIN ANALYZE 分析查询性能后再添加特定索引

COMMIT;