-- WorkMates 数据库表结构创建脚本
-- 根据 Prisma Schema 生成
-- 执行前请确保已运行 05_enums_and_types.sql

-- ================================
-- 用户相关表
-- ================================

-- 用户表
CREATE TABLE IF NOT EXISTS "users" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "email" VARCHAR(255) UNIQUE NOT NULL,
    "username" VARCHAR(100) UNIQUE,
    "phone" VARCHAR(20) UNIQUE,
    "password" VARCHAR(255),
    "name" VARCHAR(100),
    "avatar" VARCHAR(500),
    "bio" TEXT,

-- 职业信息
"position" VARCHAR(100),
    "company" VARCHAR(200),
    "experience" INTEGER,
    "industry" VARCHAR(100),
    "education" VARCHAR(200),
    "skills" TEXT[] DEFAULT '{}',

-- 用户等级和积分
"level" "UserLevel" DEFAULT 'NEWBIE',
"points" INTEGER DEFAULT 0,
"reputation" DECIMAL(10, 2) DEFAULT 0.0,

-- 隐私设置
"isAnonymous" BOOLEAN DEFAULT false,
"isEmailPublic" BOOLEAN DEFAULT false,
"isPhonePublic" BOOLEAN DEFAULT false,

-- 账户状态
"isVerified" BOOLEAN DEFAULT false,
"isActive" BOOLEAN DEFAULT true,
"isBanned" BOOLEAN DEFAULT false,

-- 时间戳
"createdAt" TIMESTAMP
WITH
    TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP
WITH
    TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "lastLogin" TIMESTAMP
WITH
    TIME ZONE,

-- 约束
CONSTRAINT check_reputation CHECK ("reputation" >= 0),
    CONSTRAINT check_points CHECK ("points" >= 0),
    CONSTRAINT check_experience CHECK ("experience" >= 0)
);

-- NextAuth.js 账户表
CREATE TABLE IF NOT EXISTS "accounts" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid (),
    "userId" UUID NOT NULL,
    "type" VARCHAR(50) NOT NULL,
    "provider" VARCHAR(50) NOT NULL,
    "providerAccountId" VARCHAR(100) NOT NULL,
    "refresh_token" TEXT,
    "access_token" TEXT,
    "expires_at" INTEGER,
    "token_type" VARCHAR(50),
    "scope" VARCHAR(200),
    "id_token" TEXT,
    "session_state" VARCHAR(200),
    CONSTRAINT fk_accounts_user FOREIGN KEY ("userId") REFERENCES "users" ("id") ON DELETE CASCADE,
    CONSTRAINT unique_provider_account UNIQUE (
        "provider",
        "providerAccountId"
    )
);

-- NextAuth.js 会话表
CREATE TABLE IF NOT EXISTS "sessions" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid (),
    "sessionToken" VARCHAR(255) UNIQUE NOT NULL,
    "userId" UUID NOT NULL,
    "expires" TIMESTAMP
    WITH
        TIME ZONE NOT NULL,
        CONSTRAINT fk_sessions_user FOREIGN KEY ("userId") REFERENCES "users" ("id") ON DELETE CASCADE
);

-- NextAuth.js 验证令牌表
CREATE TABLE IF NOT EXISTS "verification_tokens" (
    "identifier" VARCHAR(255) NOT NULL,
    "token" VARCHAR(255) UNIQUE NOT NULL,
    "expires" TIMESTAMP
    WITH
        TIME ZONE NOT NULL,
        CONSTRAINT unique_identifier_token UNIQUE ("identifier", "token")
);

-- ================================
-- 企业表
-- ================================

CREATE TABLE IF NOT EXISTS "companies" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "name" VARCHAR(200) UNIQUE NOT NULL,
    "nameEn" VARCHAR(200) UNIQUE,
    "logo" VARCHAR(500),
    "description" TEXT,
    "website" VARCHAR(500),

-- 基本信息
"industry" VARCHAR(100),
"size" "CompanySize",
"foundedYear" INTEGER,
"headquarters" VARCHAR(100),

-- 联系信息
"address" TEXT, "phone" VARCHAR(50), "email" VARCHAR(255),

-- 企业状态
"isVerified" BOOLEAN DEFAULT false, "isActive" BOOLEAN DEFAULT true,

-- 统计信息
"totalRatings" INTEGER DEFAULT 0,
"averageRating" DECIMAL(3, 2) DEFAULT 0.0,
"totalSalaries" INTEGER DEFAULT 0,
"totalReviews" INTEGER DEFAULT 0,

-- 时间戳
"createdAt" TIMESTAMP
WITH
    TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP
WITH
    TIME ZONE DEFAULT CURRENT_TIMESTAMP,

-- 约束
CONSTRAINT check_founded_year CHECK ("foundedYear" >= 1800 AND "foundedYear" <= EXTRACT(YEAR FROM CURRENT_DATE)),
    CONSTRAINT check_average_rating CHECK ("averageRating" >= 0 AND "averageRating" <= 5),
    CONSTRAINT check_total_ratings CHECK ("totalRatings" >= 0),
    CONSTRAINT check_total_salaries CHECK ("totalSalaries" >= 0),
    CONSTRAINT check_total_reviews CHECK ("totalReviews" >= 0)
);

-- ================================
-- 内容系统表
-- ================================

-- 帖子表
CREATE TABLE IF NOT EXISTS "posts" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "title" VARCHAR(500) NOT NULL,
    "content" TEXT NOT NULL,
    "excerpt" TEXT,

-- 帖子类型
"type" "PostType" DEFAULT 'DISCUSSION',
    "category" VARCHAR(100),
    "tags" TEXT[] DEFAULT '{}',

-- 关联信息
"companyId" UUID,
"authorId" UUID NOT NULL,
"isAnonymous" BOOLEAN DEFAULT false,

-- 状态
"isPublished" BOOLEAN DEFAULT false,
"isPinned" BOOLEAN DEFAULT false,
"isLocked" BOOLEAN DEFAULT false,

-- 统计
"viewCount" INTEGER DEFAULT 0,
"likeCount" INTEGER DEFAULT 0,
"commentCount" INTEGER DEFAULT 0,

-- 时间戳
"createdAt" TIMESTAMP
WITH
    TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP
WITH
    TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "publishedAt" TIMESTAMP
WITH
    TIME ZONE,

-- 外键约束
CONSTRAINT fk_posts_company FOREIGN KEY ("companyId") REFERENCES "companies" ("id") ON DELETE SET NULL,
CONSTRAINT fk_posts_author FOREIGN KEY ("authorId") REFERENCES "users" ("id") ON DELETE CASCADE,

-- 检查约束
CONSTRAINT check_view_count CHECK ("viewCount" >= 0),
    CONSTRAINT check_like_count CHECK ("likeCount" >= 0),
    CONSTRAINT check_comment_count CHECK ("commentCount" >= 0)
);

-- 评论表
CREATE TABLE IF NOT EXISTS "comments" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "content" TEXT NOT NULL,

-- 关联信息
"postId" UUID NOT NULL, "authorId" UUID NOT NULL,

-- 回复功能
"parentId" UUID,

-- 状态
"isAnonymous" BOOLEAN DEFAULT false,
"isDeleted" BOOLEAN DEFAULT false,

-- 统计
"likeCount" INTEGER DEFAULT 0,

-- 时间戳
"createdAt" TIMESTAMP
WITH
    TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP
WITH
    TIME ZONE DEFAULT CURRENT_TIMESTAMP,

-- 外键约束
CONSTRAINT fk_comments_post FOREIGN KEY ("postId") REFERENCES "posts" ("id") ON DELETE CASCADE,
CONSTRAINT fk_comments_author FOREIGN KEY ("authorId") REFERENCES "users" ("id") ON DELETE CASCADE,
CONSTRAINT fk_comments_parent FOREIGN KEY ("parentId") REFERENCES "comments" ("id") ON DELETE CASCADE,

-- 检查约束
CONSTRAINT check_comment_like_count CHECK ("likeCount" >= 0) );

-- 点赞表
CREATE TABLE IF NOT EXISTS "likes" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "userId" UUID NOT NULL,

-- 被点赞的内容 (互斥)
"postId" UUID,
"commentId" UUID,
"createdAt" TIMESTAMP
WITH
    TIME ZONE DEFAULT CURRENT_TIMESTAMP,

-- 外键约束
CONSTRAINT fk_likes_user FOREIGN KEY ("userId") REFERENCES "users" ("id") ON DELETE CASCADE,
CONSTRAINT fk_likes_post FOREIGN KEY ("postId") REFERENCES "posts" ("id") ON DELETE CASCADE,
CONSTRAINT fk_likes_comment FOREIGN KEY ("commentId") REFERENCES "comments" ("id") ON DELETE CASCADE,

-- 唯一约束 (一个用户对同一内容只能点赞一次)
CONSTRAINT unique_user_post_like UNIQUE ("userId", "postId"),
CONSTRAINT unique_user_comment_like UNIQUE ("userId", "commentId"),

-- 检查约束 (必须且只能选择一种点赞对象)
CONSTRAINT check_like_target CHECK (
        ("postId" IS NOT NULL AND "commentId" IS NULL) OR 
        ("postId" IS NULL AND "commentId" IS NOT NULL)
    )
);

-- 收藏表
CREATE TABLE IF NOT EXISTS "bookmarks" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "userId" UUID NOT NULL,
    "postId" UUID NOT NULL,
    "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

-- 外键约束
CONSTRAINT fk_bookmarks_user FOREIGN KEY ("userId") REFERENCES "users" ("id") ON DELETE CASCADE,
CONSTRAINT fk_bookmarks_post FOREIGN KEY ("postId") REFERENCES "posts" ("id") ON DELETE CASCADE,

-- 唯一约束
CONSTRAINT unique_user_post_bookmark UNIQUE ("userId", "postId") );

-- ================================
-- 数据贡献系统表
-- ================================

-- 薪资表
CREATE TABLE IF NOT EXISTS "salaries" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),

-- 基本信息
"position" VARCHAR(200) NOT NULL,
"level" VARCHAR(50),
"experience" INTEGER,

-- 薪资信息
"baseSalary" INTEGER NOT NULL,
"totalSalary" INTEGER NOT NULL,
"bonus" INTEGER,
"stockOption" INTEGER,

-- 工作信息
"workLocation" VARCHAR(100),
"workType" "EmploymentType" DEFAULT 'FULL_TIME',

-- 关联信息
"companyId" UUID NOT NULL, "authorId" UUID NOT NULL,

-- 状态
"isVerified" BOOLEAN DEFAULT false,
"isAnonymous" BOOLEAN DEFAULT true,

-- 时间戳
"createdAt" TIMESTAMP
WITH
    TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP
WITH
    TIME ZONE DEFAULT CURRENT_TIMESTAMP,

-- 外键约束
CONSTRAINT fk_salaries_company FOREIGN KEY ("companyId") REFERENCES "companies" ("id") ON DELETE CASCADE,
CONSTRAINT fk_salaries_author FOREIGN KEY ("authorId") REFERENCES "users" ("id") ON DELETE CASCADE,

-- 检查约束
CONSTRAINT check_base_salary CHECK ("baseSalary" > 0),
    CONSTRAINT check_total_salary CHECK ("totalSalary" > 0),
    CONSTRAINT check_bonus CHECK ("bonus" IS NULL OR "bonus" >= 0),
    CONSTRAINT check_stock_option CHECK ("stockOption" IS NULL OR "stockOption" >= 0),
    CONSTRAINT check_experience_salary CHECK ("experience" IS NULL OR "experience" >= 0)
);

-- 面经表
CREATE TABLE IF NOT EXISTS "interviews" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),

-- 基本信息
"position" VARCHAR(200) NOT NULL,
"department" VARCHAR(100),
"level" VARCHAR(50),

-- 面试过程
"processSteps" TEXT[] DEFAULT '{}',
    "duration" INTEGER,
    "difficulty" "InterviewDifficulty",

-- 面试内容
"questions" TEXT[] DEFAULT '{}',
    "experience" TEXT NOT NULL,
    "tips" TEXT,

-- 结果
"result" "InterviewResult",
"offer" BOOLEAN DEFAULT false,
"offerSalary" INTEGER,

-- 关联信息
"companyId" UUID NOT NULL, "authorId" UUID NOT NULL,

-- 状态
"isAnonymous" BOOLEAN DEFAULT true,

-- 时间戳
"interviewDate" TIMESTAMP
WITH
    TIME ZONE,
    "createdAt" TIMESTAMP
WITH
    TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP
WITH
    TIME ZONE DEFAULT CURRENT_TIMESTAMP,

-- 外键约束
CONSTRAINT fk_interviews_company FOREIGN KEY ("companyId") REFERENCES "companies" ("id") ON DELETE CASCADE,
CONSTRAINT fk_interviews_author FOREIGN KEY ("authorId") REFERENCES "users" ("id") ON DELETE CASCADE,

-- 检查约束
CONSTRAINT check_duration CHECK ("duration" IS NULL OR "duration" > 0),
    CONSTRAINT check_offer_salary CHECK ("offerSalary" IS NULL OR "offerSalary" > 0)
);

-- 评分表
CREATE TABLE IF NOT EXISTS "ratings" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),

-- 评分维度
"overall" DECIMAL(3, 2) NOT NULL,
"salary" DECIMAL(3, 2),
"environment" DECIMAL(3, 2),
"management" DECIMAL(3, 2),
"development" DECIMAL(3, 2),
"worklife" DECIMAL(3, 2),

-- 评价内容
"title" VARCHAR(200), "pros" TEXT, "cons" TEXT, "advice" TEXT,

-- 关联信息
"companyId" UUID NOT NULL, "authorId" UUID NOT NULL,

-- 状态
"isAnonymous" BOOLEAN DEFAULT true,
"isVerified" BOOLEAN DEFAULT false,

-- 时间戳
"createdAt" TIMESTAMP
WITH
    TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP
WITH
    TIME ZONE DEFAULT CURRENT_TIMESTAMP,

-- 外键约束
CONSTRAINT fk_ratings_company FOREIGN KEY ("companyId") REFERENCES "companies" ("id") ON DELETE CASCADE,
CONSTRAINT fk_ratings_author FOREIGN KEY ("authorId") REFERENCES "users" ("id") ON DELETE CASCADE,

-- 唯一约束 (每用户每企业只能评分一次)
CONSTRAINT unique_company_author_rating UNIQUE ("companyId", "authorId"),

-- 检查约束
CONSTRAINT check_overall_rating CHECK ("overall" >= 1 AND "overall" <= 5),
    CONSTRAINT check_salary_rating CHECK ("salary" IS NULL OR ("salary" >= 1 AND "salary" <= 5)),
    CONSTRAINT check_environment_rating CHECK ("environment" IS NULL OR ("environment" >= 1 AND "environment" <= 5)),
    CONSTRAINT check_management_rating CHECK ("management" IS NULL OR ("management" >= 1 AND "management" <= 5)),
    CONSTRAINT check_development_rating CHECK ("development" IS NULL OR ("development" >= 1 AND "development" <= 5)),
    CONSTRAINT check_worklife_rating CHECK ("worklife" IS NULL OR ("worklife" >= 1 AND "worklife" <= 5))
);

-- 举报表
CREATE TABLE IF NOT EXISTS "reports" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "reason" "ReportReason" NOT NULL,
    "description" TEXT,

-- 被举报的内容 (互斥)
"postId" UUID, "commentId" UUID,

-- 举报人
"reporterId" UUID NOT NULL,

-- 状态
"status" "ReportStatus" DEFAULT 'PENDING', "adminNote" TEXT,

-- 时间戳
"createdAt" TIMESTAMP
WITH
    TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP
WITH
    TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "resolvedAt" TIMESTAMP
WITH
    TIME ZONE,

-- 外键约束
CONSTRAINT fk_reports_post FOREIGN KEY ("postId") REFERENCES "posts" ("id") ON DELETE CASCADE,
CONSTRAINT fk_reports_comment FOREIGN KEY ("commentId") REFERENCES "comments" ("id") ON DELETE CASCADE,
CONSTRAINT fk_reports_reporter FOREIGN KEY ("reporterId") REFERENCES "users" ("id") ON DELETE CASCADE,

-- 检查约束 (必须且只能选择一种举报对象)
CONSTRAINT check_report_target CHECK (
        ("postId" IS NOT NULL AND "commentId" IS NULL) OR 
        ("postId" IS NULL AND "commentId" IS NOT NULL)
    )
);

-- ================================
-- 触发器
-- ================================

-- 自动更新 updated_at 字段的触发器
CREATE TRIGGER trigger_users_updated_at
    BEFORE UPDATE ON "users"
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_companies_updated_at
    BEFORE UPDATE ON "companies"
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_posts_updated_at
    BEFORE UPDATE ON "posts"
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_comments_updated_at
    BEFORE UPDATE ON "comments"
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_salaries_updated_at
    BEFORE UPDATE ON "salaries"
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_interviews_updated_at
    BEFORE UPDATE ON "interviews"
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_ratings_updated_at
    BEFORE UPDATE ON "ratings"
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_reports_updated_at
    BEFORE UPDATE ON "reports"
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- ================================
-- 工作经历管理系统表
-- ================================

-- 工作经历表
CREATE TABLE IF NOT EXISTS "work_experiences" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "userId" UUID NOT NULL,

-- 基本信息
"companyName" VARCHAR(200) NOT NULL,
"position" VARCHAR(100) NOT NULL,
"department" VARCHAR(100),
"employmentType" "EmploymentType" DEFAULT 'FULL_TIME',

-- 时间信息
"startDate" DATE NOT NULL,
"endDate" DATE,
"isCurrent" BOOLEAN DEFAULT false,

-- 工作内容
"description" TEXT,
    "responsibilities" TEXT[] DEFAULT '{}',
    "achievements" TEXT[] DEFAULT '{}',
    "skillsUsed" TEXT[] DEFAULT '{}',

-- 薪资信息（可选）
"salaryRange" VARCHAR(50),
"salaryCurrency" VARCHAR(10) DEFAULT 'CNY',

-- 联系信息（可选）
"supervisorName" VARCHAR(100), "supervisorContact" VARCHAR(100),

-- 审核状态
"verificationStatus" "VerificationStatus" DEFAULT 'PENDING',
"verificationScore" DECIMAL(3, 2) DEFAULT 0.00,

-- 时间戳
"createdAt" TIMESTAMP
WITH
    TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP
WITH
    TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "verifiedAt" TIMESTAMP
WITH
    TIME ZONE,
    "verifiedById" UUID,

-- 外键约束
CONSTRAINT fk_work_experiences_user FOREIGN KEY ("userId") REFERENCES "users" ("id") ON DELETE CASCADE,
CONSTRAINT fk_work_experiences_verifier FOREIGN KEY ("verifiedById") REFERENCES "users" ("id"),

-- 检查约束
CONSTRAINT check_work_exp_date_range CHECK ("startDate" <= COALESCE("endDate", CURRENT_DATE)),
    CONSTRAINT check_work_exp_verification_score CHECK ("verificationScore" >= 0 AND "verificationScore" <= 1)
);

-- 工作经历文件表
CREATE TABLE IF NOT EXISTS "experience_files" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "workExperienceId" UUID NOT NULL,

-- 文件信息
"fileName" VARCHAR(255) NOT NULL,
"fileOriginalName" VARCHAR(255) NOT NULL,
"filePath" VARCHAR(500) NOT NULL,
"fileSize" INTEGER NOT NULL,
"fileType" VARCHAR(100) NOT NULL,
"fileCategory" "FileCategory" NOT NULL,

-- 文件描述
"title" VARCHAR(200), "description" TEXT,

-- 审核状态
"verificationStatus" "VerificationStatus" DEFAULT 'PENDING',
"isPublic" BOOLEAN DEFAULT false,

-- 时间戳
"uploadedAt" TIMESTAMP
WITH
    TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "verifiedAt" TIMESTAMP
WITH
    TIME ZONE,
    "verifiedById" UUID,

-- 外键约束
CONSTRAINT fk_experience_files_work_experience FOREIGN KEY ("workExperienceId") REFERENCES "work_experiences" ("id") ON DELETE CASCADE,
CONSTRAINT fk_experience_files_verifier FOREIGN KEY ("verifiedById") REFERENCES "users" ("id"),

-- 检查约束
CONSTRAINT check_file_size CHECK ("fileSize" > 0 AND "fileSize" <= 10485760) -- 最大10MB
);

-- 审核记录表
CREATE TABLE IF NOT EXISTS "verification_records" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),

-- 审核对象
"targetType" "TargetType" NOT NULL, "targetId" UUID NOT NULL,

-- 审核信息
"reviewerId" UUID NOT NULL,
"previousStatus" VARCHAR(20),
"newStatus" VARCHAR(20) NOT NULL,

-- 审核结果
"decision" "ReviewDecision" NOT NULL,
"confidenceLevel" DECIMAL(3, 2) DEFAULT 0.00,

-- 审核详情
"reviewNotes" TEXT,
    "issuesFound" TEXT[] DEFAULT '{}',
    "requiredActions" TEXT[] DEFAULT '{}',

-- 时间戳
"createdAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

-- 外键约束
CONSTRAINT fk_verification_records_reviewer FOREIGN KEY ("reviewerId") REFERENCES "users" ("id"),

-- 检查约束
CONSTRAINT check_verification_confidence_level CHECK ("confidenceLevel" >= 0 AND "confidenceLevel" <= 1)
);

-- 用户信誉分数表
CREATE TABLE IF NOT EXISTS "user_credibility" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "userId" UUID NOT NULL UNIQUE,

-- 信誉分数
"overallScore" DECIMAL(3, 2) DEFAULT 0.00,
"workExperienceScore" DECIMAL(3, 2) DEFAULT 0.00,
"salaryContributionScore" DECIMAL(3, 2) DEFAULT 0.00,
"interviewContributionScore" DECIMAL(3, 2) DEFAULT 0.00,

-- 验证统计
"verifiedExperiencesCount" INTEGER DEFAULT 0,
"verifiedFilesCount" INTEGER DEFAULT 0,
"verifiedSalariesCount" INTEGER DEFAULT 0,
"verifiedInterviewsCount" INTEGER DEFAULT 0,

-- 贡献统计
"totalContributionsCount" INTEGER DEFAULT 0,
"helpfulContributionsCount" INTEGER DEFAULT 0,
"flaggedContributionsCount" INTEGER DEFAULT 0,

-- 时间戳
"createdAt" TIMESTAMP
WITH
    TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP
WITH
    TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "lastCalculatedAt" TIMESTAMP
WITH
    TIME ZONE DEFAULT CURRENT_TIMESTAMP,

-- 外键约束
CONSTRAINT fk_user_credibility_user FOREIGN KEY ("userId") REFERENCES "users" ("id") ON DELETE CASCADE,

-- 检查约束
CONSTRAINT check_overall_score CHECK ("overallScore" >= 0 AND "overallScore" <= 1),
    CONSTRAINT check_work_experience_score CHECK ("workExperienceScore" >= 0 AND "workExperienceScore" <= 1),
    CONSTRAINT check_salary_contribution_score CHECK ("salaryContributionScore" >= 0 AND "salaryContributionScore" <= 1),
    CONSTRAINT check_interview_contribution_score CHECK ("interviewContributionScore" >= 0 AND "interviewContributionScore" <= 1)
);

-- ================================
-- 新增表的触发器
-- ================================

CREATE TRIGGER trigger_work_experiences_updated_at
    BEFORE UPDATE ON "work_experiences"
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_user_credibility_updated_at
    BEFORE UPDATE ON "user_credibility"
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();