require('dotenv').config({ path: '.env.local' })
const { createClient } = require('@supabase/supabase-js')

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
)

async function checkRatingsTable() {
  console.log('Checking ratings table structure...\n')

  // 查询ratings表的第一条记录
  const { data, error } = await supabase.from('ratings').select('*').limit(1)

  if (error) {
    console.error('Error fetching ratings:', error)
    return
  }

  if (data && data.length > 0) {
    console.log('Ratings table columns:')
    Object.keys(data[0]).forEach(key => {
      console.log(`  - ${key}: ${typeof data[0][key]}`)
    })
    console.log('\nSample data:', JSON.stringify(data[0], null, 2))
  } else {
    console.log('No data found in ratings table')
  }
}

checkRatingsTable().catch(console.error)
