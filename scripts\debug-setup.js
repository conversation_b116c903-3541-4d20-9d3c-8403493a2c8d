#!/usr/bin/env node

/**
 * WorkMates 项目调试环境设置脚本
 *
 * 该脚本用于：
 * 1. 检查调试环境是否正确配置
 * 2. 验证必要的依赖和扩展
 * 3. 提供调试配置建议
 */

const fs = require('fs')
const { execSync } = require('child_process')

console.log('🚀 WorkMates 调试环境检查工具')
console.log('================================\n')

// 检查函数
const checks = {
  // 检查 Node.js 版本
  checkNodeVersion() {
    console.log('📦 检查 Node.js 版本...')
    const nodeVersion = process.version
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0])

    if (majorVersion >= 18) {
      console.log(`✅ Node.js 版本: ${nodeVersion} (符合要求)`)
      return true
    } else {
      console.log(`❌ Node.js 版本: ${nodeVersion} (需要 >= 18.17.0)`)
      return false
    }
  },

  // 检查必要的文件
  checkRequiredFiles() {
    console.log('\n📁 检查必要的调试配置文件...')
    const requiredFiles = [
      '.vscode/launch.json',
      '.vscode/settings.json',
      '.vscode/extensions.json',
      '.vscode/tasks.json',
    ]

    let allFilesExist = true
    requiredFiles.forEach(file => {
      if (fs.existsSync(file)) {
        console.log(`✅ ${file} 存在`)
      } else {
        console.log(`❌ ${file} 不存在`)
        allFilesExist = false
      }
    })

    return allFilesExist
  },

  // 检查环境变量文件
  checkEnvFile() {
    console.log('\n🔐 检查环境变量配置...')
    if (fs.existsSync('.env.local') || fs.existsSync('.env')) {
      console.log('✅ 环境变量文件存在')
      return true
    } else {
      console.log('⚠️  环境变量文件不存在，请从 env.example 复制')
      console.log('   运行: cp env.example .env.local')
      return false
    }
  },

  // 检查 npm 依赖
  checkDependencies() {
    console.log('\n📦 检查项目依赖...')
    try {
      if (fs.existsSync('node_modules') && fs.existsSync('package-lock.json')) {
        console.log('✅ 项目依赖已安装')
        return true
      } else {
        console.log('⚠️  项目依赖未安装，运行: npm install')
        return false
      }
    } catch (error) {
      console.log('❌ 检查依赖时出错:', error.message)
      return false
    }
  },

  // 检查数据库连接
  checkDatabase() {
    console.log('\n🗄️  检查数据库配置...')
    try {
      if (fs.existsSync('prisma/schema.prisma')) {
        console.log('✅ Prisma 模式文件存在')

        // 检查是否生成了 Prisma 客户端
        if (
          fs.existsSync('node_modules/.prisma') ||
          fs.existsSync('node_modules/@prisma/client')
        ) {
          console.log('✅ Prisma 客户端已生成')
          return true
        } else {
          console.log('⚠️  Prisma 客户端未生成，运行: npm run db:generate')
          return false
        }
      } else {
        console.log('❌ Prisma 模式文件不存在')
        return false
      }
    } catch (error) {
      console.log('❌ 检查数据库时出错:', error.message)
      return false
    }
  },

  // 检查 TypeScript 配置
  checkTypeScript() {
    console.log('\n📝 检查 TypeScript 配置...')
    try {
      if (fs.existsSync('tsconfig.json')) {
        console.log('✅ TypeScript 配置文件存在')

        // 运行类型检查
        try {
          execSync('npm run type-check', { stdio: 'pipe' })
          console.log('✅ TypeScript 类型检查通过')
          return true
        } catch {
          console.log('⚠️  TypeScript 类型检查发现错误')
          console.log('   运行 npm run type-check 查看详细信息')
          return false
        }
      } else {
        console.log('❌ TypeScript 配置文件不存在')
        return false
      }
    } catch (error) {
      console.log('❌ 检查 TypeScript 时出错:', error.message)
      return false
    }
  },
}

// 执行所有检查
async function runAllChecks() {
  const results = []

  results.push(checks.checkNodeVersion())
  results.push(checks.checkRequiredFiles())
  results.push(checks.checkEnvFile())
  results.push(checks.checkDependencies())
  results.push(checks.checkDatabase())
  results.push(checks.checkTypeScript())

  const passed = results.filter(r => r).length
  const total = results.length

  console.log('\n📊 检查结果总结')
  console.log('================')
  console.log(`✅ 通过: ${passed}/${total}`)
  console.log(`❌ 失败: ${total - passed}/${total}`)

  if (passed === total) {
    console.log('\n🎉 恭喜！调试环境配置完美！')
    console.log('\n🚀 快速开始调试：')
    console.log('   1. 按 F5 启动调试')
    console.log('   2. 或使用 Ctrl+Shift+P > "Debug: Start Debugging"')
    console.log('   3. 选择适合的调试配置')
  } else {
    console.log('\n⚠️  部分检查未通过，请根据上述提示进行修复')
    console.log('\n🔧 修复建议：')
    console.log('   1. 确保所有依赖已安装: npm install')
    console.log('   2. 配置环境变量: cp env.example .env.local')
    console.log('   3. 生成数据库客户端: npm run db:generate')
    console.log('   4. 运行类型检查: npm run type-check')
  }

  console.log('\n📚 调试模式说明：')
  console.log('   🚀 Next.js: debug server-side - 调试服务端代码')
  console.log('   🌐 Next.js: debug client-side - 调试客户端代码')
  console.log('   🔥 Next.js: debug full stack - 全栈调试')
  console.log('   🧪 Jest: debug tests - 调试测试')
  console.log('   🗄️  Prisma: studio debug - 数据库管理')

  return passed === total
}

// 主函数
if (require.main === module) {
  runAllChecks().catch(console.error)
}

module.exports = { checks, runAllChecks }
