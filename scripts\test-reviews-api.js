require('dotenv').config({ path: '.env.local' })

async function testReviewsAPI() {
  console.log('Testing Reviews API...\n')

  const baseUrl = 'http://localhost:3000'

  try {
    // 1. 首先获取一个公司ID
    console.log('1. 获取公司列表...')
    const companiesResponse = await fetch(`${baseUrl}/api/companies?limit=1`)
    const companiesData = await companiesResponse.json()

    if (!companiesData.success || !companiesData.data?.length) {
      console.log('❌ 没有找到公司数据')
      return
    }

    const companyId = companiesData.data[0].id
    console.log(
      '✅ 找到公司:',
      companiesData.data[0].name,
      '(ID:',
      companyId,
      ')'
    )

    // 2. 测试获取该公司的评价
    console.log('\n2. 测试获取公司评价...')
    const reviewsResponse = await fetch(
      `${baseUrl}/api/companies/${companyId}/reviews`
    )
    const reviewsData = await reviewsResponse.json()

    if (reviewsResponse.ok) {
      console.log('✅ 评价API响应正常')
      console.log('   - 总评价数:', reviewsData.meta?.pagination?.total || 0)
      console.log(
        '   - 平均评分:',
        reviewsData.meta?.statistics?.averageOverall || 'N/A'
      )
      console.log(
        '   - 推荐率:',
        reviewsData.meta?.statistics?.recommendationRate || 0,
        '%'
      )
    } else {
      console.log(
        '❌ 评价API响应异常:',
        reviewsData.error?.message || reviewsData.message
      )
    }

    // 3. 测试评价数据结构
    if (reviewsData.data && reviewsData.data.length > 0) {
      console.log('\n3. 检查评价数据结构...')
      const sampleReview = reviewsData.data[0]
      console.log('   - 评价字段:', Object.keys(sampleReview))

      // 检查关键字段
      const requiredFields = [
        'overallRating',
        'workLifeBalance',
        'compensation',
        'culture',
        'careerGrowth',
      ]
      const missingFields = requiredFields.filter(
        field => !(field in sampleReview)
      )

      if (missingFields.length === 0) {
        console.log('✅ 所有必需字段都存在')
      } else {
        console.log('❌ 缺少字段:', missingFields)
      }
    } else {
      console.log('\n3. 暂无评价数据，无法检查数据结构')
    }

    // 4. 测试POST接口（需要认证）
    console.log('\n4. 测试评价提交接口（不带认证）...')
    const testReviewData = {
      overallRating: 4.5,
      workLifeBalance: 4,
      compensation: 3,
      culture: 5,
      careerGrowth: 4,
      management: 3,
      title: 'API测试评价',
      pros: '这是一个测试评价的优点',
      cons: '这是一个测试评价的缺点',
      isAnonymous: true,
    }

    const postResponse = await fetch(
      `${baseUrl}/api/companies/${companyId}/reviews`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testReviewData),
      }
    )

    const postData = await postResponse.json()

    if (postResponse.status === 401) {
      console.log('✅ 认证保护正常工作 (401 Unauthorized)')
    } else if (postResponse.ok) {
      console.log('✅ 评价提交成功（意外通过认证）')
    } else {
      console.log('⚠️  其他错误:', postData.error?.message || postData.message)
    }
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message)

    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 请确保开发服务器正在运行: npm run dev')
    }
  }

  console.log('\n测试完成!')
}

testReviewsAPI()
