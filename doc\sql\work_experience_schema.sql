-- WorkMates 工作经历管理数据库表结构
-- 用于用户工作经历、文件上传和审核功能

-- ================================
-- 工作经历表
-- ================================
CREATE TABLE work_experiences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- 基本信息
    company_name VARCHAR(200) NOT NULL,
    position VARCHAR(100) NOT NULL,
    department VARCHAR(100),
    employment_type VARCHAR(50) DEFAULT 'FULL_TIME', -- FULL_TIME, PART_TIME, CONTRACT, INTERNSHIP
    
    -- 时间信息
    start_date DATE NOT NULL,
    end_date DATE, -- NULL 表示仍在职
    is_current BOOLEAN DEFAULT false,
    
    -- 工作内容
    description TEXT,
    responsibilities TEXT[], -- 工作职责数组
    achievements TEXT[], -- 主要成就数组
    skills_used TEXT[], -- 使用的技能数组
    
    -- 薪资信息（可选）
    salary_range VARCHAR(50), -- 如 "15k-25k"
    salary_currency VARCHAR(10) DEFAULT 'CNY',
    
    -- 联系信息（可选）
    supervisor_name VARCHAR(100),
    supervisor_contact VARCHAR(100),
    
    -- 审核状态
    verification_status VARCHAR(20) DEFAULT 'PENDING', -- PENDING, APPROVED, REJECTED, REVOKED
    verification_score DECIMAL(3,2) DEFAULT 0.00, -- 0.00-1.00 信誉分数
    
    -- 元数据
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    verified_at TIMESTAMP WITH TIME ZONE,
    verified_by UUID REFERENCES users(id),
    
    -- 约束
    CONSTRAINT check_date_range CHECK (start_date <= COALESCE(end_date, CURRENT_DATE)),
    CONSTRAINT check_verification_score CHECK (verification_score >= 0 AND verification_score <= 1),
    CONSTRAINT check_verification_status CHECK (verification_status IN ('PENDING', 'APPROVED', 'REJECTED', 'REVOKED'))
);

-- ================================
-- 工作经历文件表
-- ================================
CREATE TABLE experience_files (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    work_experience_id UUID NOT NULL REFERENCES work_experiences(id) ON DELETE CASCADE,
    
    -- 文件信息
    file_name VARCHAR(255) NOT NULL,
    file_original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    file_type VARCHAR(100) NOT NULL, -- image/jpeg, application/pdf, etc.
    file_category VARCHAR(50) NOT NULL, -- CONTRACT, CERTIFICATE, PHOTO, DOCUMENT, OTHER
    
    -- 文件描述
    title VARCHAR(200),
    description TEXT,
    
    -- 审核状态
    verification_status VARCHAR(20) DEFAULT 'PENDING', -- PENDING, APPROVED, REJECTED
    is_public BOOLEAN DEFAULT false, -- 是否公开显示
    
    -- 元数据
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    verified_at TIMESTAMP WITH TIME ZONE,
    verified_by UUID REFERENCES users(id),
    
    -- 约束
    CONSTRAINT check_file_size CHECK (file_size > 0 AND file_size <= 10485760), -- 最大10MB
    CONSTRAINT check_file_category CHECK (file_category IN ('CONTRACT', 'CERTIFICATE', 'PHOTO', 'DOCUMENT', 'OTHER')),
    CONSTRAINT check_file_verification_status CHECK (verification_status IN ('PENDING', 'APPROVED', 'REJECTED'))
);

-- ================================
-- 审核记录表
-- ================================
CREATE TABLE verification_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- 审核对象
    target_type VARCHAR(50) NOT NULL, -- WORK_EXPERIENCE, EXPERIENCE_FILE, SALARY, INTERVIEW
    target_id UUID NOT NULL,
    
    -- 审核信息
    reviewer_id UUID NOT NULL REFERENCES users(id),
    previous_status VARCHAR(20),
    new_status VARCHAR(20) NOT NULL,
    
    -- 审核结果
    decision VARCHAR(20) NOT NULL, -- APPROVED, REJECTED, REVOKED, PENDING_MORE_INFO
    confidence_level DECIMAL(3,2) DEFAULT 0.00, -- 0.00-1.00 审核置信度
    
    -- 审核详情
    review_notes TEXT,
    issues_found TEXT[], -- 发现的问题数组
    required_actions TEXT[], -- 需要的改进措施
    
    -- 时间信息
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- 约束
    CONSTRAINT check_target_type CHECK (target_type IN ('WORK_EXPERIENCE', 'EXPERIENCE_FILE', 'SALARY', 'INTERVIEW')),
    CONSTRAINT check_decision CHECK (decision IN ('APPROVED', 'REJECTED', 'REVOKED', 'PENDING_MORE_INFO')),
    CONSTRAINT check_confidence_level CHECK (confidence_level >= 0 AND confidence_level <= 1)
);

-- ================================
-- 用户信誉分数表
-- ================================
CREATE TABLE user_credibility (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL UNIQUE REFERENCES users(id) ON DELETE CASCADE,
    
    -- 信誉分数
    overall_score DECIMAL(3,2) DEFAULT 0.00, -- 0.00-1.00 总体信誉分数
    work_experience_score DECIMAL(3,2) DEFAULT 0.00, -- 工作经历可信度
    salary_contribution_score DECIMAL(3,2) DEFAULT 0.00, -- 薪资贡献可信度
    interview_contribution_score DECIMAL(3,2) DEFAULT 0.00, -- 面经贡献可信度
    
    -- 验证统计
    verified_experiences_count INT DEFAULT 0,
    verified_files_count INT DEFAULT 0,
    verified_salaries_count INT DEFAULT 0,
    verified_interviews_count INT DEFAULT 0,
    
    -- 贡献统计
    total_contributions_count INT DEFAULT 0,
    helpful_contributions_count INT DEFAULT 0,
    flagged_contributions_count INT DEFAULT 0,
    
    -- 时间信息
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_calculated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- 约束
    CONSTRAINT check_overall_score CHECK (overall_score >= 0 AND overall_score <= 1),
    CONSTRAINT check_work_experience_score CHECK (work_experience_score >= 0 AND work_experience_score <= 1),
    CONSTRAINT check_salary_contribution_score CHECK (salary_contribution_score >= 0 AND salary_contribution_score <= 1),
    CONSTRAINT check_interview_contribution_score CHECK (interview_contribution_score >= 0 AND interview_contribution_score <= 1)
);

-- ================================
-- 索引
-- ================================

-- 工作经历表索引
CREATE INDEX idx_work_experiences_user_id ON work_experiences(user_id);
CREATE INDEX idx_work_experiences_company ON work_experiences(company_name);
CREATE INDEX idx_work_experiences_position ON work_experiences(position);
CREATE INDEX idx_work_experiences_verification_status ON work_experiences(verification_status);
CREATE INDEX idx_work_experiences_date_range ON work_experiences(start_date, end_date);
CREATE INDEX idx_work_experiences_current ON work_experiences(is_current) WHERE is_current = true;

-- 文件表索引
CREATE INDEX idx_experience_files_work_experience_id ON experience_files(work_experience_id);
CREATE INDEX idx_experience_files_verification_status ON experience_files(verification_status);
CREATE INDEX idx_experience_files_category ON experience_files(file_category);
CREATE INDEX idx_experience_files_public ON experience_files(is_public) WHERE is_public = true;

-- 审核记录表索引
CREATE INDEX idx_verification_records_target ON verification_records(target_type, target_id);
CREATE INDEX idx_verification_records_reviewer ON verification_records(reviewer_id);
CREATE INDEX idx_verification_records_created_at ON verification_records(created_at);

-- 用户信誉表索引
CREATE INDEX idx_user_credibility_user_id ON user_credibility(user_id);
CREATE INDEX idx_user_credibility_overall_score ON user_credibility(overall_score DESC);

-- ================================
-- 触发器函数
-- ================================

-- 自动更新updated_at字段
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 创建触发器
CREATE TRIGGER update_work_experiences_updated_at BEFORE UPDATE ON work_experiences
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_credibility_updated_at BEFORE UPDATE ON user_credibility
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ================================
-- 存储函数
-- ================================

-- 计算用户信誉分数
CREATE OR REPLACE FUNCTION calculate_user_credibility(p_user_id UUID)
RETURNS VOID AS $$
DECLARE
    v_work_exp_score DECIMAL(3,2) := 0.00;
    v_verified_exp_count INT := 0;
    v_total_exp_count INT := 0;
    v_overall_score DECIMAL(3,2) := 0.00;
BEGIN
    -- 计算工作经历验证分数
    SELECT 
        COUNT(*) FILTER (WHERE verification_status = 'APPROVED'),
        COUNT(*)
    INTO v_verified_exp_count, v_total_exp_count
    FROM work_experiences
    WHERE user_id = p_user_id;
    
    -- 计算工作经历可信度分数
    IF v_total_exp_count > 0 THEN
        v_work_exp_score := LEAST(1.00, v_verified_exp_count::DECIMAL / v_total_exp_count);
    END IF;
    
    -- 计算总体信誉分数 (可以根据需要调整权重)
    v_overall_score := v_work_exp_score * 0.6 + 
                      COALESCE((SELECT salary_contribution_score FROM user_credibility WHERE user_id = p_user_id), 0.00) * 0.2 +
                      COALESCE((SELECT interview_contribution_score FROM user_credibility WHERE user_id = p_user_id), 0.00) * 0.2;
    
    -- 更新或插入用户信誉记录
    INSERT INTO user_credibility (
        user_id, overall_score, work_experience_score, 
        verified_experiences_count, last_calculated_at
    ) VALUES (
        p_user_id, v_overall_score, v_work_exp_score, 
        v_verified_exp_count, CURRENT_TIMESTAMP
    )
    ON CONFLICT (user_id) DO UPDATE SET
        overall_score = v_overall_score,
        work_experience_score = v_work_exp_score,
        verified_experiences_count = v_verified_exp_count,
        updated_at = CURRENT_TIMESTAMP,
        last_calculated_at = CURRENT_TIMESTAMP;
        
END;
$$ LANGUAGE plpgsql;

-- 自动更新用户信誉分数的触发器
CREATE OR REPLACE FUNCTION trigger_update_user_credibility()
RETURNS TRIGGER AS $$
BEGIN
    -- 当工作经历审核状态变化时，重新计算用户信誉分数
    IF TG_OP = 'UPDATE' AND OLD.verification_status != NEW.verification_status THEN
        PERFORM calculate_user_credibility(NEW.user_id);
    ELSIF TG_OP = 'INSERT' THEN
        PERFORM calculate_user_credibility(NEW.user_id);
    ELSIF TG_OP = 'DELETE' THEN
        PERFORM calculate_user_credibility(OLD.user_id);
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- 创建触发器
CREATE TRIGGER trigger_work_experiences_credibility_update
    AFTER INSERT OR UPDATE OR DELETE ON work_experiences
    FOR EACH ROW EXECUTE FUNCTION trigger_update_user_credibility();

-- ================================
-- 视图
-- ================================

-- 用户工作经历汇总视图
CREATE OR REPLACE VIEW user_work_experience_summary AS
SELECT 
    u.id as user_id,
    u.username,
    u.name,
    COUNT(we.id) as total_experiences,
    COUNT(we.id) FILTER (WHERE we.verification_status = 'APPROVED') as verified_experiences,
    COUNT(we.id) FILTER (WHERE we.verification_status = 'PENDING') as pending_experiences,
    COUNT(we.id) FILTER (WHERE we.verification_status = 'REJECTED') as rejected_experiences,
    COUNT(ef.id) as total_files,
    COUNT(ef.id) FILTER (WHERE ef.verification_status = 'APPROVED') as verified_files,
    COALESCE(uc.overall_score, 0.00) as credibility_score,
    COALESCE(uc.work_experience_score, 0.00) as work_experience_score
FROM users u
LEFT JOIN work_experiences we ON u.id = we.user_id
LEFT JOIN experience_files ef ON we.id = ef.work_experience_id
LEFT JOIN user_credibility uc ON u.id = uc.user_id
WHERE u.is_active = true
GROUP BY u.id, u.username, u.name, uc.overall_score, uc.work_experience_score;

-- 待审核内容视图
CREATE OR REPLACE VIEW pending_verifications AS
SELECT 
    'WORK_EXPERIENCE' as content_type,
    we.id as content_id,
    we.user_id,
    u.username,
    u.name as user_name,
    we.company_name,
    we.position,
    we.created_at,
    we.verification_status,
    COUNT(ef.id) as attached_files
FROM work_experiences we
JOIN users u ON we.user_id = u.id
LEFT JOIN experience_files ef ON we.id = ef.work_experience_id
WHERE we.verification_status = 'PENDING'
GROUP BY we.id, we.user_id, u.username, u.name, we.company_name, we.position, we.created_at, we.verification_status

UNION ALL

SELECT 
    'EXPERIENCE_FILE' as content_type,
    ef.id as content_id,
    we.user_id,
    u.username,
    u.name as user_name,
    ef.file_original_name as company_name,
    ef.file_category as position,
    ef.uploaded_at as created_at,
    ef.verification_status,
    1 as attached_files
FROM experience_files ef
JOIN work_experiences we ON ef.work_experience_id = we.id
JOIN users u ON we.user_id = u.id
WHERE ef.verification_status = 'PENDING'

ORDER BY created_at DESC;

COMMIT; 