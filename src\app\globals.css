@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-sans antialiased;
  }
}

/* WorkMates 品牌样式 */
@layer components {
  .workmates-gradient {
    @apply bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600;
  }
  
  .workmates-card {
    @apply bg-white rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200;
  }
  
  .workmates-btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-2 rounded-lg transition-colors duration-200;
  }
  
  .workmates-btn-secondary {
    @apply bg-gray-100 hover:bg-gray-200 text-gray-900 font-medium px-6 py-2 rounded-lg transition-colors duration-200;
  }
}

/* 文本样式改进 */
@layer utilities {
  .text-readable {
    @apply text-gray-900 dark:text-gray-100;
  }
  
  .text-muted-readable {
    @apply text-gray-600 dark:text-gray-300;
  }
  
  .text-subtle {
    @apply text-gray-500 dark:text-gray-400;
  }
}

/* 富文本编辑器样式 */
.prose {
  @apply max-w-none text-gray-900;
}

.prose h1,
.prose h2,
.prose h3,
.prose h4 {
  @apply font-semibold text-gray-900;
}

.prose p {
  @apply text-gray-700 leading-relaxed;
}

.prose blockquote {
  @apply border-l-4 border-blue-200 pl-4 italic text-gray-600 bg-blue-50 py-2;
}

.prose code {
  @apply bg-gray-100 text-gray-800 px-1 py-0.5 rounded text-sm;
}

.prose pre {
  @apply bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto;
}

.prose a {
  @apply text-blue-600 hover:text-blue-800 hover:underline;
}

/* 表单控件样式改进 */
.form-input {
  @apply border-gray-300 focus:border-blue-500 focus:ring-blue-500 rounded-lg px-4 py-2.5 text-gray-900 placeholder-gray-500;
}

.form-label {
  @apply text-gray-700 font-medium text-sm mb-2 block;
}

/* 加载动画 */
@keyframes pulse-subtle {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.animate-pulse-subtle {
  animation: pulse-subtle 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-up {
  animation: slide-up 0.2s ease-out;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 响应式工具类 */
@layer utilities {
  .container-responsive {
    @apply container mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .grid-responsive {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6;
  }
  
  .flex-responsive {
    @apply flex flex-col sm:flex-row gap-4;
  }
}

/* 状态指示器 */
.status-online {
  @apply bg-green-500;
}

.status-away {
  @apply bg-yellow-500;
}

.status-offline {
  @apply bg-gray-400;
}

/* 焦点管理和可访问性 */
.focus-visible {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
}

/* 悬停效果改进 */
.hover-lift {
  @apply hover:transform hover:-translate-y-1 transition-transform duration-200;
}

.hover-scale {
  @apply hover:scale-105 transition-transform duration-200;
}

/* 徽章和标签样式 */
.badge-primary {
  @apply bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium;
}

.badge-success {
  @apply bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium;
}

.badge-warning {
  @apply bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium;
}

.badge-error {
  @apply bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-medium;
} 

/* 导航菜单对齐修复 - 强制左对齐解决方案 */
/* 给导航菜单项目设置相对定位 */
[data-radix-navigation-menu-item]:has([data-radix-navigation-menu-trigger]) {
  position: relative !important;
}

/* 强制下拉菜单viewport左对齐 */
[data-radix-navigation-menu-viewport] {
  position: absolute !important;
  left: 0 !important;
  right: auto !important;
  top: 100% !important;
  transform: none !important;
}

/* 确保下拉菜单内容也左对齐 */
[data-radix-navigation-menu-content] {
  position: relative !important;
  left: 0 !important;
  right: auto !important;
  transform: none !important;
}

/* 针对特定导航菜单的更精确控制 */
.navigation-menu-override [data-radix-navigation-menu-viewport] {
  position: absolute !important;
  left: 0 !important;
  transform: translateX(0) !important;
}

/* 最强力的左对齐解决方案 - 直接覆盖所有可能的定位 */
.navigation-menu-override [data-radix-navigation-menu-viewport],
.navigation-menu-override [data-radix-navigation-menu-viewport] > div {
  position: absolute !important;
  left: 0 !important;
  right: auto !important;
  transform: none !important;
  margin-left: 0 !important;
  margin-right: auto !important;
}

/* 确保触发器和内容的关系正确 */
.navigation-menu-override [data-radix-navigation-menu-item] {
  position: relative !important;
}

/* 禁用任何可能影响定位的动画和变换 */
.navigation-menu-override [data-radix-navigation-menu-viewport],
.navigation-menu-override [data-radix-navigation-menu-content] {
  animation: none !important;
  transition: none !important;
}

/* 可读性工具类 - 修复白色文字问题 */
.text-readable {
  @apply text-gray-900;
}

.text-muted-readable {
  @apply text-gray-600;
}

/* 品牌一致性 */
.workmates-gradient {
  @apply bg-gradient-to-r from-blue-600 to-purple-600;
}

.workmates-card {
  @apply bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow;
}

/* 状态指示器 */
.status-verified {
  @apply bg-green-100 text-green-800 border-green-200;
}

.status-pending {
  @apply bg-yellow-100 text-yellow-800 border-yellow-200;
}

.status-unverified {
  @apply bg-gray-100 text-gray-600 border-gray-200;
}

/* 响应式工具 */
.container-responsive {
  @apply container mx-auto px-4 sm:px-6 lg:px-8;
}

.grid-responsive {
  @apply grid gap-4 sm:gap-6 lg:gap-8;
}

/* 滚动条优化 */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  @apply bg-gray-100 rounded-full;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded-full hover:bg-gray-400;
}

/* 按钮悬停效果增强 */
.btn-hover-scale {
  @apply transform transition-transform duration-200 hover:scale-105;
}

/* 卡片悬停效果 */
.card-hover {
  @apply transition-all duration-200 hover:shadow-lg hover:-translate-y-1;
}

/* 加载动画 */
.loading-spin {
  @apply animate-spin;
}

.loading-pulse {
  @apply animate-pulse;
}

/* 文本溢出处理 */
.text-ellipsis-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-ellipsis-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 表单控件增强 */
.form-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .auto-dark {
    @apply bg-gray-900 text-white;
  }
  
  .auto-dark .workmates-card {
    @apply bg-gray-800 border-gray-700;
  }
}

:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 221.2 83.2% 53.3%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 221.2 83.2% 53.3%;
  --chart-1: 12 76% 61%;
  --chart-2: 173 58% 39%;
  --chart-3: 197 37% 24%;
  --chart-4: 43 74% 66%;
  --chart-5: 27 87% 67%;
  --radius: 0.5rem;
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 217.2 91.2% 59.8%;
  --primary-foreground: 222.2 84% 4.9%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 224.3 76.3% 94.1%;
  --chart-1: 220 70% 50%;
  --chart-2: 160 60% 45%;
  --chart-3: 30 80% 55%;
  --chart-4: 280 65% 60%;
  --chart-5: 340 75% 55%;
}

* {
  border-color: hsl(var(--border));
}

body {
  color: hsl(var(--foreground));
  background: hsl(var(--background));
} 