require('dotenv').config({ path: '.env.local' })
const { createClient } = require('@supabase/supabase-js')

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
)

async function checkDatabaseStatus() {
  console.log('Checking database status...\n')

  const tables = ['companies', 'users', 'ratings', 'salaries', 'interviews']

  for (const table of tables) {
    try {
      console.log(`📊 检查 ${table} 表...`)

      const { error, count } = await supabase
        .from(table)
        .select('*', { count: 'exact', head: true })

      if (error) {
        console.log(`   ❌ 错误: ${error.message}`)
      } else {
        console.log(`   ✅ 记录数: ${count || 0}`)

        if (count > 0) {
          // 获取一条示例数据查看字段
          const { data: sample, error: sampleError } = await supabase
            .from(table)
            .select('*')
            .limit(1)

          if (!sampleError && sample?.length > 0) {
            console.log(`   📝 字段: ${Object.keys(sample[0]).join(', ')}`)
          }
        }
      }
      console.log('')
    } catch (err) {
      console.log(`   ❌ 异常: ${err.message}\n`)
    }
  }
}

checkDatabaseStatus()
