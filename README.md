# 🚀 WorkMates - 职场信息分享平台

> 一个功能完整的职场信息分享平台，帮助职场人士获取真实的企业信息、薪资数据和面试经验。

## 📋 项目介绍

WorkMates 是一个基于 **Next.js 15** 和 **Supabase** 构建的现代化职场信息分享平台。

### ✅ **已完成功能** (98%完成度)

- 🏢 **企业信息系统** - 完整的企业查询、详情展示、评分统计
- 💰 **薪资数据管理** - 匿名薪资分享、统计分析、数据可视化
- 📝 **面试经验分享** - 面试过程记录、经验分享、难度评估
- 👥 **职场论坛社区** - 发帖讨论、评论互动、嵌套回复
- 🔐 **用户认证系统** - Google OAuth、邮箱注册、安全登录
- 👤 **用户资料管理** - 个人信息、工作经历、隐私设置
- 📤 **文件上传系统** - 头像上传、工作证明文件管理
- 🔍 **智能搜索系统** - 企业搜索、帖子搜索、用户搜索、全局搜索
- 📊 **数据统计分析** - 薪资趋势、评分分布、活跃度分析

## 🛠️ 技术栈

### 前端技术

- **框架**: Next.js 15 (App Router)
- **语言**: TypeScript
- **样式**: TailwindCSS + shadcn/ui
- **UI组件**: Radix UI
- **表单**: React Hook Form + Zod
- **图标**: Lucide React

### 后端技术

- **数据库**: Supabase (PostgreSQL)
- **ORM**: Prisma
- **认证**: NextAuth.js v5
- **API**: Next.js API Routes
- **文件上传**: 本地存储 + 文件验证

### 开发工具

- **包管理**: pnpm
- **代码规范**: ESLint + Prettier
- **类型检查**: TypeScript
- **部署**: Vercel
- **数据库管理**: Prisma Studio

## 🚀 快速开始

### 环境要求

- Node.js 18.0+
- npm 或 yarn
- Supabase 账户

### 1. 克隆项目

```bash
git clone https://github.com/your-username/WorkMates.git
cd WorkMates
```

### 2. 安装依赖

```bash
npm install
# 或
yarn install
```

### 3. 环境配置

```bash
# 复制环境变量文件
cp env.example .env.local

# 编辑 .env.local 文件，填入您的 Supabase 配置
```

### 4. 数据库配置

```bash
# 生成 Prisma 客户端
npx prisma generate

# 同步数据库结构
npx prisma db pull
```

### 5. 启动开发服务器

```bash
npm run dev
# 或
yarn dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

## 📁 项目结构

```
WorkMates/
├── app/                    # NextJS 13+ App Router
│   ├── (auth)/            # 认证相关页面
│   │   ├── login/         # 登录页面
│   │   └── register/      # 注册页面
│   ├── (dashboard)/       # 主应用页面
│   │   ├── companies/     # 企业信息页面
│   │   ├── salaries/      # 薪资信息页面
│   │   ├── interviews/    # 面试经验页面
│   │   └── profile/       # 个人资料页面
│   ├── api/               # API 路由
│   │   ├── auth/          # 认证 API
│   │   ├── companies/     # 企业 API
│   │   ├── salaries/      # 薪资 API
│   │   └── interviews/    # 面试 API
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 首页
├── components/            # 可复用组件
│   ├── ui/               # 基础UI组件
│   ├── forms/            # 表单组件
│   ├── features/         # 功能组件
│   └── layout/           # 布局组件
├── lib/                   # 工具库
│   ├── auth.ts           # 认证配置
│   ├── database.ts       # 数据库连接
│   ├── utils.ts          # 工具函数
│   └── validations.ts    # 数据验证
├── hooks/                # 自定义 Hooks
├── types/                # TypeScript 类型定义
├── prisma/               # Prisma 配置
│   └── schema.prisma     # 数据库模型
├── doc/                  # 项目文档
│   ├── design/           # 设计文档
│   └── sql/              # SQL 脚本
├── public/               # 静态资源
├── .env.example          # 环境变量示例
├── package.json
└── README.md
```

## 🗄️ 数据库结构

### 核心表分类

| 系统类型 | 表数量 | 主要功能               |
| -------- | ------ | ---------------------- |
| 用户系统 | 4张    | 用户管理、认证、会话   |
| 企业系统 | 1张    | 企业信息、评分统计     |
| 内容系统 | 4张    | 帖子、评论、点赞、收藏 |
| 数据分享 | 3张    | 薪资、面试、评分数据   |
| 管理系统 | 1张    | 内容举报和管理         |
| 工作经历 | 4张    | 经历验证、文件管理     |

### 数据库特性

- ✅ 完整的关系型设计
- ✅ 数据完整性约束
- ✅ 自动时间戳更新
- ✅ 索引优化
- ✅ 枚举类型定义

## 🔧 环境变量

在 `.env.local` 文件中配置以下变量：

```env
# Supabase 配置
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# 数据库配置
DATABASE_URL=your_database_url
DIRECT_URL=your_direct_database_url

# NextAuth 配置
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=http://localhost:3000
```

## 📚 开发文档

详细的开发文档位于 `doc/design/` 目录：

- 📋 [项目需求文档](doc/design/WorkMates项目需求文档.md)
- 🛠️ [完整开发指南](doc/design/WorkMates完整开发指南.md)
- 🗄️ [数据库设计文档](doc/design/WorkMates数据库完整设计文档.md)
- 📁 [项目结构说明](doc/design/项目结构说明.md)
- 🎉 [项目完成总结](doc/design/WorkMates项目完成总结.md)

## 🚀 部署指南

### Vercel 部署

1. 连接 GitHub 仓库到 Vercel
2. 配置环境变量
3. 自动部署

### 环境变量配置

确保在 Vercel 中配置所有必要的环境变量。

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。详情请参阅 [LICENSE](LICENSE) 文件。

## 📞 联系我们

- 项目维护者: [Your Name]
- 邮箱: <EMAIL>
- 项目地址: [GitHub Repository]

---

**🎊 感谢您对 WorkMates 项目的关注！**

如果您觉得这个项目有价值，请给我们一个 ⭐️ 星标！
